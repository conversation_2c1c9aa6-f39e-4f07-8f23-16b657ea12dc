<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta name="theme-color" content="#0c73e9">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">

    <!-- تحسين عناوين الصفحة لـ SEO - محاكي القدرة المعرفية -->
    <title>محاكي اختبار القدرة المعرفية 2025 | أسئلة القدرة المعرفية المحاكية | المركز الوطني للقياس</title>
    <meta name="description" content="محاكي اختبار القدرة المعرفية 2025 ✓ تدرب على 2,500+ سؤال محلول ✓ نماذج قياس محاكية 100% ✓ اختبار القدرة المعرفية ✓ تحضير كامل للاختبار ✓ نماذج المركز الوطني للقياس ✓ شرح تفصيلي مجاني">
    <meta name="keywords" content="محاكي القدرة المعرفية, اختبار القدرة المعرفية 2025, قدرة معرفية, cognitive ability test, المركز الوطني للقياس, اختبار قدرة معرفية تجريبي, نماذج قدرة معرفية محلولة, تحضير القدرة المعرفية">

    <!-- تحسين Meta Tags للـ SEO -->
    <meta name="language" content="Arabic">
    <meta name="subject" content="اختبار القدرة المعرفية - المركز الوطني للقياس">
    <meta name="copyright" content="مجتهد - Mujtahid 2025">
    <meta name="revised" content="Monday, April 17, 2025">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="محاكي اختبار القدرة المعرفية 2025 | تدرب الآن مجاناً">
    <meta property="og:description" content="تدرب على اختبار القدرة المعرفية 2025 مع أكثر من 2,500 سؤال محلول. شرح تفصيلي واختبارات تجريبية مجانية مطابقة للمركز الوطني للقياس">
    <meta property="og:image" content="https://mujtahidacademy.com/images/quiz.PNG">
    <meta property="og:url" content="https://mujtahidacademy.com/cognitive">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="أكاديمية مجتهد">
    <meta property="og:locale" content="ar_SA">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://mujtahidacademy.com/cognitive">

    <!-- تضمين ملفات CSS والخطوط -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@400;500;700;900&family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">

    <!-- تحسين Favicon -->
    <link rel="icon" type="image/x-icon" href="images/favicon/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="images/favicon/apple-touch-icon.png">
</head>

<body>
    <!-- شريط علوي جديد -->
    <div class="top-bar">
        محاكاة حقيقية 100% لاختبار القدرة المعرفية 2025
        <span class="animated-icon">🧠</span>
        محدّث لنماذج المركز الوطني للقياس
    </div>

    <main>
        <!-- قسم القدرة المعرفية -->
        <section class="hero qudrat-hero">
            <div class="logo">
                <a href="index.html">
                    <img src="images/logo1.png" alt="شعار تطبيق مجتهد - محاكي اختبارات قياس 2025">
                </a>
            </div>

            <div class="hero-content">
                <div class="hero-text">
                    <h1>محاكي اختبار القدرة المعرفية 2025</h1>
                    <p class="hero-subtitle">🧠 تدرب الآن على أكثر من 2,500 سؤال محلول مع شرح تفصيلي واحصل على درجة عالية في اختبار القدرة المعرفية</p>

                    <div class="features-list">
                        <ul>
                            <li>محاكاة دقيقة 100% لنظام اختبار القدرة المعرفية الرسمي من المركز الوطني للقياس</li>
                            <li>تغطية شاملة لجميع أنواع الأسئلة المعرفية والذهنية</li>
                            <li>أسئلة مماثلة تماماً لأنماط وصعوبة المركز الوطني للقياس</li>
                            <li>شرح تفصيلي خطوة بخطوة لكل سؤال مع الاستراتيجيات</li>
                            <li>نماذج محاكية بنفس مستوى الصعوبة والوقت للاختبار الحقيقي</li>
                            <li>تتبع مستوى تقدمك وتحليل نقاط ضعفك مع خطة تحسين شخصية</li>
                        </ul>
                    </div>

                    <div class="cta-buttons">
                        <a href="#cognitive-sections" class="primary-btn">
                            <i class="fas fa-rocket"></i>
                            استكشف محتويات القدرة المعرفية
                        </a>
                        <a href="#download-app" class="secondary-btn">
                            <i class="fas fa-download"></i>
                            تحميل التطبيق الآن
                        </a>
                    </div>
                </div>

                <div class="qudrat-image">
                    <img src="images/app.PNG" alt="محاكي اختبار القدرة المعرفية 2025" class="qudrat-preview">
                    <div class="badge-qudrat">🔥 محدث 2025</div>
                </div>
            </div>
        </section>

        <!-- أقسام القدرة المعرفية -->
        <section class="qudrat-sections" id="cognitive-sections">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">أقسام اختبار القدرة المعرفية</h2>
                    <p class="section-subtitle">تدرب على جميع أنواع الأسئلة المعرفية والذهنية المطلوبة في الاختبار</p>
                </div>

                <div class="qudrat-parts">
                    <div class="qudrat-part">
                        <div class="part-header">
                            <i class="fas fa-puzzle-piece"></i>
                            <h3>الاستدلال المنطقي</h3>
                        </div>
                        <div class="part-content">
                            <ul class="part-list">
                                <li>الاستدلال الاستقرائي والاستنباطي</li>
                                <li>تحليل الأنماط والتسلسلات</li>
                                <li>الاستنتاج المنطقي من المعطيات</li>
                                <li>حل المشكلات المنطقية</li>
                                <li>التفكير النقدي والتحليلي</li>
                            </ul>
                            <p class="part-count">💎 أكثر من 600 سؤال محلول بالتفصيل</p>
                        </div>
                    </div>

                    <div class="qudrat-part">
                        <div class="part-header">
                            <i class="fas fa-calculator"></i>
                            <h3>الاستدلال الكمي</h3>
                        </div>
                        <div class="part-content">
                            <ul class="part-list">
                                <li>العمليات الحسابية الأساسية</li>
                                <li>النسب والتناسب والمعدلات</li>
                                <li>تفسير البيانات والجداول</li>
                                <li>الهندسة الأساسية والقياس</li>
                                <li>حل المسائل الرياضية التطبيقية</li>
                            </ul>
                            <p class="part-count">💎 أكثر من 600 سؤال محلول بالتفصيل</p>
                        </div>
                    </div>

                    <div class="qudrat-part">
                        <div class="part-header">
                            <i class="fas fa-language"></i>
                            <h3>الاستدلال اللفظي</h3>
                        </div>
                        <div class="part-content">
                            <ul class="part-list">
                                <li>فهم المعاني والمفردات</li>
                                <li>التناظر اللفظي والعلاقات</li>
                                <li>إكمال الجمل والنصوص</li>
                                <li>الاستيعاب القرائي والتحليل</li>
                                <li>الاستنتاج من النصوص</li>
                            </ul>
                            <p class="part-count">💎 أكثر من 600 سؤال محلول بالتفصيل</p>
                        </div>
                    </div>

                    <div class="qudrat-part">
                        <div class="part-header">
                            <i class="fas fa-shapes"></i>
                            <h3>الاستدلال المكاني</h3>
                        </div>
                        <div class="part-content">
                            <ul class="part-list">
                                <li>التصور المكاني والهندسي</li>
                                <li>تحليل الأشكال والأنماط</li>
                                <li>التدوير والانعكاس</li>
                                <li>العلاقات المكانية</li>
                                <li>حل المسائل البصرية</li>
                            </ul>
                            <p class="part-count">💎 أكثر من 700 سؤال محلول بالتفصيل</p>
                        </div>
                    </div>
                </div>

                <div class="qudrat-benefits">
                    <div class="benefit">
                        <div class="benefit-icon">
                            <i class="fas fa-stopwatch"></i>
                        </div>
                        <h4>محاكاة الوقت الحقيقي</h4>
                        <p>اختبار محاكي بنفس وقت الاختبار الرسمي (120 دقيقة) مع عداد تنازلي دقيق</p>
                    </div>

                    <div class="benefit">
                        <div class="benefit-icon">
                            <i class="fas fa-desktop"></i>
                        </div>
                        <h4>واجهة مطابقة 100%</h4>
                        <p>نفس واجهة اختبار القدرة المعرفية الرسمي من المركز الوطني للقياس تماماً</p>
                    </div>

                    <div class="benefit">
                        <div class="benefit-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h4>تحليل الأداء المتقدم</h4>
                        <p>تقارير تفصيلية لمستوى أدائك مع تحديد نقاط القوة والضعف في كل نوع</p>
                    </div>

                    <div class="benefit">
                        <div class="benefit-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h4>استراتيجيات الحل السريع</h4>
                        <p>أفضل الاستراتيجيات والحيل لحل الأسئلة بسرعة ودقة عالية</p>
                    </div>

                    <div class="benefit">
                        <div class="benefit-icon">
                            <i class="fas fa-target"></i>
                        </div>
                        <h4>تدريب مخصص</h4>
                        <p>خطة تدريب شخصية مبنية على مستواك ونقاط ضعفك في كل نوع</p>
                    </div>

                    <div class="benefit">
                        <div class="benefit-icon">
                            <i class="fas fa-medal"></i>
                        </div>
                        <h4>ضمان التحسن</h4>
                        <p>نظام متابعة يضمن تحسن درجاتك مع كل اختبار تجريبي</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- تحميل التطبيق -->
        <section class="download-app" id="download-app">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">حمل تطبيق محاكي القدرة المعرفية الآن</h2>
                    <p class="section-subtitle">تدرب في أي وقت وأي مكان - التطبيق متوفر مجاناً</p>
                </div>

                <div class="download-content">
                    <div class="app-info">
                        <div class="app-features">
                            <div class="app-feature">
                                <i class="fas fa-check-circle"></i>
                                <span>أكثر من 2,500 سؤال قدرة معرفية محلول بالتفصيل</span>
                            </div>
                            <div class="app-feature">
                                <i class="fas fa-check-circle"></i>
                                <span>نظام محاكاة طبق الأصل لاختبار المركز الوطني للقياس</span>
                            </div>
                            <div class="app-feature">
                                <i class="fas fa-check-circle"></i>
                                <span>محاكي لوضع الاختبار بنفس الوقت والأسئلة</span>
                            </div>
                            <div class="app-feature">
                                <i class="fas fa-check-circle"></i>
                                <span>شرح تفصيلي لجميع الأسئلة بأسلوب مبسط</span>
                            </div>
                            <div class="app-feature">
                                <i class="fas fa-check-circle"></i>
                                <span>تحديث مستمر لأنماط الأسئلة الجديدة</span>
                            </div>
                        </div>

                        <div class="download-buttons">
                            <a href="https://apps.apple.com/ae/app/مجتهد/id6605936809" class="download-btn apple" target="_blank" rel="noopener">
                                <i class="fab fa-apple"></i>
                                <span>
                                    <small>متوفر على</small>
                                    <strong>App Store</strong>
                                </span>
                            </a>

                            <a href="https://play.google.com/store/apps/details?id=com.mazen.flutterquiz" class="download-btn google" target="_blank" rel="noopener">
                                <i class="fab fa-google-play"></i>
                                <span>
                                    <small>متوفر على</small>
                                    <strong>Google Play</strong>
                                </span>
                            </a>
                        </div>
                    </div>

                    <div class="app-image">
                        <img src="images/app.PNG" alt="تطبيق محاكي اختبار القدرة المعرفية مجتهد" class="app-screen">
                    </div>
                </div>
            </div>
        </section>

        <!-- الأسئلة الشائعة حول اختبار القدرة المعرفية -->
        <section class="qudrat-faq">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">أسئلة شائعة عن اختبار القدرة المعرفية</h2>
                    <p class="section-subtitle">إجابات على الأسئلة الشائعة حول اختبار القدرة المعرفية 2025</p>
                </div>

                <div class="faq-grid">
                    <div class="faq-item">
                        <h3>ما هو اختبار القدرة المعرفية وما أهميته؟</h3>
                        <p>اختبار القدرة المعرفية هو اختبار يقيس القدرات الذهنية والمعرفية للفرد في مجالات مختلفة مثل الاستدلال المنطقي والكمي واللفظي والمكاني. يُستخدم للقبول في الوظائف الحكومية والبرامج التدريبية.</p>
                    </div>

                    <div class="faq-item">
                        <h3>كم عدد أسئلة اختبار القدرة المعرفية؟</h3>
                        <p>يتكون اختبار القدرة المعرفية من حوالي 100 سؤال موزعة على أربعة أقسام: الاستدلال المنطقي، الاستدلال الكمي، الاستدلال اللفظي، والاستدلال المكاني. مدة الاختبار ساعتان (120 دقيقة).</p>
                    </div>

                    <div class="faq-item">
                        <h3>ما هي أنواع الأسئلة في القدرة المعرفية؟</h3>
                        <p>يشمل الاختبار أسئلة الاستدلال المنطقي (حل المشكلات والأنماط)، الاستدلال الكمي (الرياضيات التطبيقية)، الاستدلال اللفظي (فهم النصوص والمفردات)، والاستدلال المكاني (التصور الهندسي والأشكال).</p>
                    </div>

                    <div class="faq-item">
                        <h3>ما هي درجات اختبار القدرة المعرفية؟</h3>
                        <p>تتراوح درجات اختبار القدرة المعرفية من 20 إلى 100 درجة. الدرجات العالية (80+) تعتبر ممتازة، والدرجات المتوسطة (60-79) جيدة، والدرجات المنخفضة (أقل من 60) تحتاج إلى تحسين.</p>
                    </div>

                    <div class="faq-item">
                        <h3>كيف أحضر لاختبار القدرة المعرفية بفعالية؟</h3>
                        <p>أفضل طريقة هي التدرب على نماذج محاكية للاختبار مع تطوير المهارات المعرفية المختلفة. تطبيق مجتهد يوفر محاكاة دقيقة مع شرح تفصيلي لكل سؤال وتغطية شاملة لجميع الأنواع.</p>
                    </div>

                    <div class="faq-item">
                        <h3>متى يمكنني إعادة اختبار القدرة المعرفية؟</h3>
                        <p>يمكن إعادة اختبار القدرة المعرفية في الفترات المحددة من المركز الوطني للقياس. ينصح بالتحضير الجيد قبل الإعادة لضمان تحسن الدرجة، والتدرب المكثف على النماذج المحاكية.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="images/logo1.png" alt="شعار مجتهد - محاكي اختبارات قياس 2025" loading="lazy">
                    <p>تطبيق مجتهد هو الحل الشامل للتفوق في اختبار القدرة المعرفية وجميع اختبارات قياس. يوفر التطبيق محاكيات مطابقة 100% للاختبار الرسمي مع أكثر من 10,000 سؤال محلول وشرح تفصيلي.</p>
                </div>

                <div class="footer-links">
                    <h3 class="footer-title">محاكيات قياس</h3>
                    <a href="qudrat.html" class="footer-link">محاكي القدرات العامة</a>
                    <a href="tahsili.html" class="footer-link">محاكي التحصيلي</a>
                    <a href="step.html" class="footer-link">محاكي STEP</a>
                    <a href="cognitive.html" class="footer-link">محاكي القدرة المعرفية</a>
                    <a href="university.html" class="footer-link">محاكي قدرات الجامعيين</a>
                    <a href="license.html" class="footer-link">محاكي الرخصة المهنية</a>
                </div>

                <div class="footer-links">
                    <h3 class="footer-title">روابط مهمة</h3>
                    <a href="privacy-policy.html" class="footer-link">سياسة الخصوصية</a>
                    <a href="terms.html" class="footer-link">شروط الاستخدام</a>
                    <a href="#" class="footer-link">المساعدة والدعم</a>
                    <a href="#" class="footer-link">اتصل بنا</a>
                    <a href="index.html" class="footer-link">الرئيسية</a>
                </div>

                <div class="footer-contact">
                    <h3 class="footer-title">تواصل معنا</h3>
                    <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span dir="ltr">+966 581105341</span>
                    </div>
                    <div class="contact-info">
                        <i class="fab fa-whatsapp"></i>
                        <span dir="ltr">+966 581105341</span>
                    </div>

                    <div class="social-links">
                        <a href="#" class="social-link" target="_blank" aria-label="تويتر">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link" target="_blank" aria-label="انستغرام">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-link" target="_blank" aria-label="تيك توك">
                            <i class="fab fa-tiktok"></i>
                        </a>
                        <a href="#" class="social-link" target="_blank" aria-label="يوتيوب">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>© 2025 مجتهد - محاكي اختبار القدرة المعرفية | جميع الحقوق محفوظة | التطبيق الأول لتحضير اختبارات قياس في المملكة العربية السعودية</p>
            </div>
        </div>
    </footer>

    <!-- سكريبت محسن لأداء الصفحة -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });

            document.querySelectorAll('.qudrat-part, .benefit, .app-feature, .faq-item').forEach(item => {
                observer.observe(item);
            });

            // تأثير التمرير السلس للروابط الداخلية
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // تأثير التمرير للصور
            const images = document.querySelectorAll('.qudrat-preview, .app-screen');
            images.forEach(img => {
                img.addEventListener('mouseenter', function() {
                    this.style.transform = 'perspective(1000px) rotateY(0deg) rotateX(0deg) scale(1.05)';
                });

                img.addEventListener('mouseleave', function() {
                    if (this.classList.contains('qudrat-preview')) {
                        this.style.transform = 'perspective(1000px) rotateY(-10deg) rotateX(5deg)';
                    } else {
                        this.style.transform = 'perspective(1000px) rotateY(10deg) rotateX(-5deg)';
                    }
                });
            });

            // تتبع النقرات على أزرار التحميل
            document.querySelectorAll('.download-btn, .primary-btn, .secondary-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>