<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#0c73e9">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="msapplication-TileImage" content="images/favicon/web-app-manifest-192x192.png">
    <meta name="msapplication-TileColor" content="#0c73e9">

    <!-- Enhanced SEO Meta Tags -->
    <title>محاكي اختبارات قياس 2025 مجاني | قدرات تحصيلي STEP رخصة مهنية | مجتهد</title>
    <meta name="description" content="محاكي قياس مجاني 2025 ✓ اختبار قدرات تجريبي ✓ اختبار تحصيلي تجريبي ✓ محاكي STEP ✓ الرخصة المهنية ✓ القدرة المعرفية ✓ 10,000+ سؤال محلول ✓ شرح تفصيلي ✓ نماذج محدثة ✓ التطبيق الأول في السعودية">
    <meta name="keywords" content="محاكي قياس مجاني, اختبار قياس تجريبي 2025, اختبار قياس محاكي مجانا, محاكاة قدرات, محاكي تحصيلي جديد, اختبار القدرات المحوسب, تجريبي قياس اونلاين, التحصيلي تجريبي, نماذج قياس محلولة, قدرات تدريب, محاكي STEP, محاكي القدرة المعرفية, اختبار قياس, قياس, قدرات, تحصيلي, اختبار قدرات, اختبار تحصيلي, اختبار قياس 2025, قياس 2025, قدرات 2025, تحصيلي 2025, اختبار قدرات 2025, اختبار تحصيلي 2025">

    <!-- Additional Meta Tags for Better SEO -->
    <meta name="language" content="Arabic">
    <meta name="subject" content="اختبارات قياس تجريبية">
    <meta name="copyright" content="مجتهد - Mujtahid">
    <meta name="revised" content="Thursday, April 17, 2025">

    <!-- Advanced Schema Markup -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@graph": [
        {
          "@type": "WebSite",
          "@id": "https://mujtahidacademy.com/#website",
          "url": "https://mujtahidacademy.com",
          "name": "محاكي اختبارات قياس - مجتهد",
          "description": "المنصة الأولى لمحاكاة جميع اختبارات قياس",
          "inLanguage": ["ar-SA", "en"]
        },
        {
          "@type": "WebPage",
          "@id": "https://mujtahidacademy.com/#webpage",
          "url": "https://mujtahidacademy.com",
          "name": "محاكي اختبارات قياس",
          "description": "محاكي اختبار القدرات والتحصيلي والقدرة المعرفية",
          "keywords": "محاكي قياس, محاكي قدرات, محاكي تحصيلي"
        },
        {
          "@type": "EducationalApplication",
          "name": "تطبيق مجتهد لاختبارات قياس",
          "applicationCategory": "EducationalApplication",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "SAR"
          },
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "ratingCount": "50000",
            "bestRating": "5",
            "worstRating": "1"
          }
        },
        {
          "@type": "FAQPage",
          "mainEntity": [
            {
              "@type": "Question",
              "name": "كيف أتدرب على اختبار قياس مجاناً؟",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "يوفر تطبيق مجتهد أكثر من 10,000 سؤال تجريبي محلول مع شرح تفصيلي لجميع أنواع اختبارات قياس: القدرات، التحصيلي، STEP، والقدرة المعرفية"
              }
            },
            {
              "@type": "Question",
              "name": "ما هي أفضل طريقة للتحضير لاختبار القدرات؟",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "التدرب على نماذج محاكية لاختبار قياس مع حل أسئلة تجريبية وفهم الشرح التفصيلي لكل سؤال. تطبيق مجتهد يوفر نماذج محدثة 2024 تحاكي نمط أسئلة الاختبار"
              }
            }
          ]
        }
      ]
    }
    </script>

    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "محاكي اختبار قياس - مجتهد",
      "applicationCategory": "EducationalApplication",
      "operatingSystem": "Any",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "SAR"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "ratingCount": "50000",
        "bestRating": "5",
        "worstRating": "1"
      },
      "downloadUrl": [
        "https://apps.apple.com/ae/app/مجتهد/id6605936809",
        "https://play.google.com/store/apps/details?id=com.mazen.flutterquiz"
      ]
    }
    </script>

    <!-- Enhanced Open Graph Meta Tags -->
    <meta property="og:title" content="أكاديمية مجتهد | تحضير شامل لجميع اختبارات قياس">
    <meta property="og:description" content="تدرب مع أكثر من 10,000 سؤال محلول في القدرات والتحصيلي وSTEP. شرح تفصيلي واختبارات تجريبية مجانية لضمان تفوقك في اختبارات قياس.">
    <meta property="og:image" content="https://mujtahidacademy.com/images/social-share.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:url" content="https://mujtahidacademy.com">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="أكاديمية مجتهد">
    <meta property="og:locale" content="ar_SA">

    <!-- Enhanced Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@mujtahidacademy">
    <meta name="twitter:creator" content="@mujtahidacademy">
    <meta name="twitter:title" content="أكاديمية مجتهد | تحضير شامل لاختبارات قياس">
    <meta name="twitter:description" content="تدرب مع أكثر من 10,000 سؤال محلول في القدرات والتحصيلي وSTEP. شرح تفصيلي واختبارات تجريبية مجانية.">
    <meta name="twitter:image" content="https://mujtahidacademy.com/images/social-share.jpg">

    <!-- Additional SEO Meta Tags -->
    <meta name="author" content="أكاديمية مجتهد">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="google" content="nositelinkssearchbox">
    <meta name="google-site-verification" content="google55a732897415da9d">
    <meta name="apple-itunes-app" content="app-id=6605936809">
    <meta name="google-play-app" content="app-id=com.mazen.flutterquiz">

    <!-- إضافة كلمات مفتاحية إضافية للمحتوى العربي -->
    <meta name="keywords" lang="ar" content="اختبار قياس, محاكي قياس, اختبار قدرات, اختبار تحصيلي, قياس 2025, قدرات 2025, تحصيلي 2025, اختبار قياس تجريبي, اختبار قياس محاكي, اختبار قدرات محاكي, اختبار تحصيلي محاكي, تطبيق مجتهد, أكاديمية مجتهد, تحضير قياس, تحضير قدرات, تحضير تحصيلي, نماذج قياس, نماذج قدرات, نماذج تحصيلي, أسئلة قياس, أسئلة قدرات, أسئلة تحصيلي, شرح قياس, شرح قدرات, شرح تحصيلي, تدريب قياس, تدريب قدرات, تدريب تحصيلي, اختبار STEP, اختبار القدرة المعرفية, اختبار قدرات الجامعيين, الرخصة المهنية للمعلمين">

    <!-- إضافة علامات للمشاركة على وسائل التواصل الاجتماعي -->
    <meta property="article:publisher" content="https://www.facebook.com/mujtahidacademy">
    <meta property="article:modified_time" content="2025-05-02T13:32:00+03:00">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://www.mujtahidacademy.com">

    <!-- Alternate Language Versions -->
    <link rel="alternate" hreflang="ar-SA" href="https://www.mujtahidacademy.com">
    <link rel="alternate" hreflang="x-default" href="https://www.mujtahidacademy.com/en">

    <!-- Icons - Load asynchronously -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"></noscript>

    <!-- Enhanced Fonts with preload -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@400;500;700;900&family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">

    <!-- Preload critical fonts -->
    <link rel="preload" href="https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1l_6gLuvPDQ.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://fonts.gstatic.com/s/notokufiarabic/v18/CSRg4yNNh-GbW3o3JkwoDcdvMqNhQYcLUn8.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Preload critical resources -->
    <link rel="preload" href="styles.css" as="style">
    <link rel="preload" href="images/logo1.png" as="image">
    <link rel="preload" href="images/app.PNG" as="image">

    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">

    <!-- Favicon -->
    <link rel="icon" href="images/favicon/favicon.ico" />
    <link rel="icon" type="image/png" href="images/favicon/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/png" href="images/favicon/web-app-manifest-192x192.png" sizes="192x192" />
    <link rel="icon" type="image/svg+xml" href="images/favicon/favicon.svg" />
    <link rel="shortcut icon" href="images/favicon/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="images/favicon/apple-touch-icon.png" />
    <link rel="manifest" href="manifest.json" />
    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">

    <!-- Twitter conversion tracking base code -->
    <script>
    !function(e,t,n,s,u,a){e.twq||(s=e.twq=function(){s.exe?s.exe.apply(s,arguments):s.queue.push(arguments);
    },s.version='1.1',s.queue=[],u=t.createElement(n),u.async=!0,u.src='https://static.ads-twitter.com/uwt.js',
    a=t.getElementsByTagName(n)[0],a.parentNode.insertBefore(u,a))}(window,document,'script');
    twq('config','pa23z');
    </script>
    <!-- End Twitter conversion tracking base code -->

    <!-- إضافة Meta Description باللغة العربية والإنجليزية -->
    <meta name="description" lang="ar" content="محاكي قياس 2025 - تدرب الآن على اختبارات القدرات والتحصيلي وSTEP مع أكثر من 10,000 سؤال محلول وشرح تفصيلي مجاني">
    <meta name="description" lang="en" content="Qiyas Simulator 2025 - Practice now for Qudrat, Tahsili and STEP tests with over 10,000 solved questions and free detailed explanations">

    <!-- إضافة Structured Data للتطبيق التعليمي -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "MobileApplication",
      "name": "مجتهد - محاكي اختبارات قياس",
      "operatingSystem": ["iOS", "Android"],
      "applicationCategory": "EducationalApplication",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "SAR"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "ratingCount": "50000"
      }
    }
    </script>

    <!-- إضافة Structured Data للمراجعات -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Product",
      "name": "تطبيق مجتهد لاختبارات قياس",
      "description": "تطبيق محاكاة لاختبارات قياس يوفر أكثر من 10,000 سؤال محلول مع شرح تفصيلي",
      "image": "https://mujtahidacademy.com/images/logo1.png",
      "brand": {
        "@type": "Brand",
        "name": "مجتهد"
      },
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "SAR",
        "availability": "https://schema.org/InStock"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "reviewCount": "50000",
        "bestRating": "5",
        "worstRating": "1"
      },
      "review": [
        {
          "@type": "Review",
          "author": {
            "@type": "Person",
            "name": "أحمد س."
          },
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": "5",
            "bestRating": "5",
            "worstRating": "1"
          },
          "datePublished": "2025-04-15",
          "reviewBody": "ساعدني التطبيق كثيراً في التحضير لاختبار القدرات. النماذج المحاكية دقيقة جداً والشرح ممتاز!"
        },
        {
          "@type": "Review",
          "author": {
            "@type": "Person",
            "name": "محمد ع."
          },
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": "5",
            "bestRating": "5",
            "worstRating": "1"
          },
          "datePublished": "2025-04-10",
          "reviewBody": "أفضل تطبيق للتحضير لاختبارات قياس. شرح تفصيلي ونماذج محاكية 100%"
        },
        {
          "@type": "Review",
          "author": {
            "@type": "Person",
            "name": "سارة م."
          },
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": "5",
            "bestRating": "5",
            "worstRating": "1"
          },
          "datePublished": "2025-04-05",
          "reviewBody": "تطبيق رائع! ساعدني في رفع درجاتي في التحصيلي بشكل كبير"
        }
      ]
    }
    </script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=AW-16930945793"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'AW-16930945793', {
            'cookie_flags': 'SameSite=None;Secure',
            'allow_ad_personalization_signals': false, // احترام خصوصية المستخدم
            'page_title': 'محاكي جميع اختبارات قياس 2025 | مجتهد',
            'page_location': 'https://www.mujtahidacademy.com',
            'send_page_view': true
        });
    </script>

    <!-- Consent Mode Setup -->
    <script>
        // Default consent settings
        gtag('consent', 'default', {
            'ad_storage': 'denied',
            'analytics_storage': 'denied',
            'wait_for_update': 500
        });

        // Function to update consent
        function updateConsent(type, value) {
            gtag('consent', 'update', {
                [type]: value
            });
        }
    </script>

    <!-- Track App Download Conversion -->
    <script>
        document.querySelectorAll('.download-btn').forEach(button => {
            button.addEventListener('click', function(e) {
                const isIOS = this.classList.contains('apple');

                // Send conversion to Google Ads
                gtag('event', 'conversion', {
                    'send_to': 'AW-16930945793/app_download',
                    'value': 1.0,
                    'currency': 'SAR',
                    'transaction_id': `${Date.now()}_${isIOS ? 'ios' : 'android'}`
                });
            });
        });
    </script>

    <!-- تحديث الـ style في head -->
    <style>
        /* Critical CSS for performance */
        html {
            scroll-behavior: auto !important;
            -webkit-overflow-scrolling: touch;
            font-display: swap;
        }

        body {
            overflow-y: auto;
            overscroll-behavior-y: none;
            margin: 0;
            padding: 0;
            position: relative;
            font-family: 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }

        /* Preload critical images */
        .hero {
            background-image: none;
            will-change: auto;
        }

        /* Optimize font loading */
        @font-face {
            font-family: 'Tajawal';
            font-display: swap;
            font-weight: 400;
            src: local('Tajawal Regular'), local('Tajawal-Regular');
        }

        /* تبسيط الأنيميشن */
        .stat-item {
            opacity: 0;
            transform: none !important;
            transition: opacity 0.5s ease-out;
            will-change: opacity;
        }

        .stat-item.visible {
            opacity: 1;
        }

        /* إزالة التأثيرات التي تؤثر على الأداء */
        .app-showcase {
            transform: none !important;
            will-change: auto;
        }

        .image-glow {
            display: none;
        }

        /* تثبيت العناصر المتحركة */
        .features-grid,
        .screenshots-grid,
        .reviews-grid {
            transform: translateZ(0);
            backface-visibility: hidden;
        }

        /* أنماط قسم المشاركة */
        .share-section {
            margin: 30px 0;
            text-align: center;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
        }

        .share-title {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #0c73e9;
        }

        .share-subtitle {
            font-size: 1rem;
            margin-bottom: 20px;
            color: #666;
        }

        .share-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .share-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            color: white;
            font-size: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .share-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .share-btn.facebook {
            background-color: #3b5998;
        }

        .share-btn.twitter {
            background-color: #1da1f2;
        }

        .share-btn.whatsapp {
            background-color: #25d366;
        }

        .share-btn.telegram {
            background-color: #0088cc;
        }

        /* أنماط قسم النشرة البريدية */
        .newsletter {
            padding: 50px 0;
            background-color: #f0f7ff;
        }

        .newsletter-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            padding: 30px;
            gap: 30px;
        }

        .newsletter-content {
            flex: 1;
        }

        .newsletter-title {
            font-size: 1.8rem;
            color: #0c73e9;
            margin-bottom: 15px;
        }

        .newsletter-subtitle {
            font-size: 1rem;
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .newsletter-form .form-group {
            display: flex;
            margin-bottom: 15px;
        }

        .newsletter-form input[type="email"] {
            flex: 1;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px 0 0 5px;
            font-size: 1rem;
            font-family: 'Tajawal', sans-serif;
        }

        .newsletter-form .subscribe-btn {
            background-color: #0c73e9;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 0 5px 5px 0;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s;
            font-family: 'Tajawal', sans-serif;
            font-weight: 600;
        }

        .newsletter-form .subscribe-btn:hover {
            background-color: #0a5dc0;
        }

        .form-privacy {
            font-size: 0.85rem;
            color: #777;
        }

        .form-privacy a {
            color: #0c73e9;
            text-decoration: none;
        }

        .form-privacy a:hover {
            text-decoration: underline;
        }

        .newsletter-image {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        @media (max-width: 768px) {
            .newsletter-container {
                flex-direction: column;
                text-align: center;
            }

            .newsletter-form .form-group {
                flex-direction: column;
            }

            .newsletter-form input[type="email"] {
                border-radius: 5px;
                margin-bottom: 10px;
            }

            .newsletter-form .subscribe-btn {
                border-radius: 5px;
            }
        }

        /* أنماط رسالة النجاح */
        .success-message {
            background-color: #e7f7e7;
            color: #2e7d32;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            font-size: 1.1rem;
            margin: 20px 0;
            border: 1px solid #c8e6c9;
        }

        /* أنماط روابط المميزات */
        .feature-link {
            display: inline-block;
            margin-top: 15px;
            color: #0c73e9;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
        }

        .feature-link:hover {
            color: #0a5dc0;
            border-bottom-color: #0a5dc0;
            transform: translateX(-3px);
        }

        .feature-card:hover .feature-link {
            color: #0a5dc0;
        }
    </style>
</head>
<body>
    <!-- تعديل الهيدر -->
    <!-- <header class="main-header">
        <div class="container">
            <div class="header-content">
                <div class="logo-container">
                    <img src="images/logo1.png" alt="مجتهد" class="header-logo">
                </div> -->
                <!-- حذف زر التحميل من الهيدر -->
            <!-- </div>
        </div>
    </header> -->

    <main>
        <section class="hero">
            <div class="logo">
                <img src="images/logo1.png" alt="شعار تطبيق مجتهد">
            </div>

            <div class="hero-content">
                <div class="hero-text">
                    <h1>محاكي اختبارات قياس</h1>
                    <p class="hero-subtitle">طريقك للتفوق في اختبارات قياس</p>
                    <div class="features-list">
                        <ul>
                            <li>أكثر من 10,000 سؤال محلول</li>
                            <li>نماذج محاكية 100% لاختبارات قياس</li>
                            <li>شرح تفصيلي لكل خطوة</li>
                            <li>تغطية شاملة للقدرات والتحصيلي</li>
                            <li>تحديثات مستمرة لنماذج 2024</li>
                            <li>دعم فني على مدار الساعة</li>
                        </ul>
                    </div>

                    <div class="download-buttons">
                        <a href="https://apps.apple.com/ae/app/مجتهد/id6605936809" class="download-btn apple" target="_blank" rel="noopener" aria-label="تحميل تطبيق مجتهد من App Store">
                            <i class="fab fa-apple"></i>
                            <span>
                                <small>متوفر على</small>
                                <strong>App Store</strong>
                            </span>
                        </a>

                        <a href="https://play.google.com/store/apps/details?id=com.mazen.flutterquiz" class="download-btn google" target="_blank" rel="noopener" aria-label="تحميل تطبيق مجتهد من Google Play">
                            <i class="fab fa-google-play"></i>
                            <span>
                                <small>متوفر على</small>
                                <strong>Google Play</strong>
                            </span>
                        </a>
                    </div>

                    <!-- تصحيح نسبة الرضا -->
                    <div class="stats">
                        <div class="stat-item">
                            <div class="stat-value">10K+</div>
                            <div class="stat-label">سؤال محلول</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">10K+</div>
                            <div class="stat-label">طالب مستفيد</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">95%</div>
                            <div class="stat-label">نسبة الرضا</div>
                        </div>
                    </div>
                </div>

                <div class="app-showcase">
                    <div class="app-badge">جديد</div>
                    <img src="images/app.PNG" alt="واجهة تطبيق مجتهد لاختبارات قياس" class="app-preview" loading="lazy" width="360" height="720" decoding="async">
                    <div class="image-glow"></div>
                </div>
            </div>
        </section>

        <!-- تحسين قسم المميزات -->
        <section class="features" id="features">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">مميزات محاكي قياس</h2>
                    <p class="section-subtitle">كل ما تحتاجه للتفوق في اختبارات قياس في مكان واحد</p>
                </div>
                <div class="features-grid">
                    <article class="feature-card highlight">
                        <div class="feature-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h3><a href="qudrat.html" style="color: inherit; text-decoration: none;">محاكي القدرات العامة</a></h3>
                        <p>نماذج محاكاة دقيقة للقدرات تشمل:</p>
                        <ul class="feature-list">
                            <li>القسم الكمي بجميع أنماطه</li>
                            <li>القسم اللفظي بجميع مهاراته</li>
                            <li>شرح تفصيلي لكل سؤال</li>
                        </ul>
                        <a href="qudrat.html" class="feature-link">استكشف القدرات ←</a>
                    </article>

                    <article class="feature-card highlight">
                        <div class="feature-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <h3><a href="tahsili.html" style="color: inherit; text-decoration: none;">محاكي التحصيلي</a></h3>
                        <p>نماذج شاملة للتحصيلي بجميع المسارات:</p>
                        <ul class="feature-list">
                            <li>المسار العلمي بكل تخصصاته</li>
                            <li>المسار الأدبي والشرعي</li>
                            <li>تغطية كاملة للمنهج</li>
                        </ul>
                        <a href="tahsili.html" class="feature-link">استكشف التحصيلي ←</a>
                    </article>

                    <article class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h3><a href="cognitive.html" style="color: inherit; text-decoration: none;">محاكي القدرة المعرفية</a></h3>
                        <p>اختبارات تجريبية متخصصة تشمل جميع مهارات القدرة المعرفية مع نصائح وإرشادات للحل.</p>
                        <a href="cognitive.html" class="feature-link">استكشف القدرة المعرفية ←</a>
                    </article>

                    <article class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-university"></i>
                        </div>
                        <h3><a href="university.html" style="color: inherit; text-decoration: none;">محاكي قدرات الجامعيين</a></h3>
                        <p>نماذج محاكاة احترافية لقدرات الجامعيين مع تدريبات مكثفة على أنماط الأسئلة.</p>
                        <a href="university.html" class="feature-link">استكشف قدرات الجامعيين ←</a>
                    </article>

                    <article class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-language"></i>
                        </div>
                        <h3><a href="step.html" style="color: inherit; text-decoration: none;">محاكي STEP</a></h3>
                        <p>تدريبات شاملة على اختبار STEP مع تغطية كاملة لجميع مهارات اللغة الإنجليزية المطلوبة.</p>
                        <a href="step.html" class="feature-link">استكشف STEP ←</a>
                    </article>

                    <article class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <h3><a href="license.html" style="color: inherit; text-decoration: none;">محاكي الرخصة المهنية</a></h3>
                        <p>اختبارات الرخصة المهنية للمعلمين:</p>
                        <ul class="feature-list">
                            <li>الرخصة المهنية العامة</li>
                            <li>الرخصة المهنية التخصصية</li>
                            <li>جميع التخصصات التعليمية</li>
                        </ul>
                        <a href="license.html" class="feature-link">استكشف الرخصة المهنية ←</a>
                    </article>
                </div>
            </div>
        </section>

        <!-- تحديث قسم عرض صور التطبيق -->
        <section class="app-screenshots" id="screenshots">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">نظرة داخل التطبيق</h2>
                    <p class="section-subtitle">تجربة مستخدم متميزة وتصميم عصري</p>
                </div>
                <div class="screenshots-grid">
                    <div class="screenshot-card">
                        <img src="images/home.PNG" alt="الشاشة الرئيسية - اختيار نوع الاختبار" class="screenshot-image" loading="lazy" width="300" height="600">
                        <div class="screenshot-overlay">
                            <h3>الشاشة الرئيسية</h3>
                            <p>اختر نوع الاختبار الذي تريد التدرب عليه</p>
                        </div>
                    </div>
                    <div class="screenshot-card">
                        <img src="images/quiz.PNG" alt="واجهة الاختبار - أسئلة تفاعلية" class="screenshot-image" loading="lazy" width="300" height="600">
                        <div class="screenshot-overlay">
                            <h3>واجهة الاختبار</h3>
                            <p>أسئلة تفاعلية مع شرح تفصيلي</p>
                        </div>
                    </div>
                    <div class="screenshot-card">
                        <img src="images/leaderborard.PNG" alt="واجهة التصنيف العام" class="screenshot-image" loading="lazy" width="300" height="600">
                        <div class="screenshot-overlay">
                            <h3>واجهة التصنيف العام</h3>
                            <p>تنافس مع الطلاب الآخرين</p>
                    <!-- <div class="screenshot-card"></div>
                        <img src="images/explain.PNG" alt="قسم التدريب والمراجعة" class="screenshot-image">
                        <div class="screenshot-overlay">
                            <h3>قسم التدريب</h3>
                            <p>تدرب على أسئلة محددة حسب المهارة</p>
                        </div> -->
                    </div>
                    <!-- <div class="screenshot-card">
                        <img src="images/explain.PNG" alt="المراجعة والشرح التفصيلي" class="screenshot-image">
                        <div class="screenshot-overlay">
                            <h3>الشرح التفصيلي</h3>
                            <p>شرح خطوات الحل بالتفصيل</p>
                        </div> -->

                </div>
            </div>
        </section>

        <!-- إضافة قسم جديد للمراجعات -->
        <section class="reviews" id="reviews">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">آراء الطلاب</h2>
                    <p class="section-subtitle">ماذا يقول طلابنا عن تجربتهم مع تطبيق مجتهد</p>
                </div>
                <div class="reviews-grid">
                    <div class="review-card">
                        <div class="review-rating">★★★★★</div>
                        <p class="review-text">ساعدني التطبيق كثيراً في التحضير لاختبار القدرات. النماذج المحاكية دقيقة جداً والشرح ممتاز!</p>
                        <div class="review-author">أحمد س.</div>
                    </div>
                    <div class="review-card">
                        <div class="review-rating">★★★★★</div>
                        <p class="review-text">أفضل تطبيق للتحضير لاختبارات قياس. شرح تفصيلي ونماذج محاكية 100%</p>
                        <div class="review-author">محمد ع.</div>
                    </div>
                    <div class="review-card">
                        <div class="review-rating">★★★★★</div>
                        <p class="review-text">تطبيق رائع! ساعدني في رفع درجاتي في التحصيلي بشكل كبير</p>
                        <div class="review-author">سارة م.</div>
                    </div>
                </div>
            </div>
        </section>

        إضافة قسم الأسئلة الشائعة
        <section class="faq" id="faq">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">الأسئلة الشائعة</h2>
                    <p class="section-subtitle">إجابات على أكثر الأسئلة شيوعاً</p>
                </div>
                <div class="faq-grid">
                    <div class="faq-item">
                        <h3>كيف يمكنني البدء في استخدام التطبيق؟</h3>
                        <p>قم بتحميل التطبيق من متجر التطبيقات وابدأ التدرب مجاناً على آلاف الأسئلة المحلولة.</p>
                    </div>
                    <div class="faq-item">
                        <h3>هل التطبيق مجاني بالكامل؟</h3>
                        <p>نعم، التطبيق مجاني 100% مع إمكانية الوصول لجميع المحتوى التعليمي.</p>
                    </div>
                    <div class="faq-item">
                        <h3>كم عدد النماذج المتوفرة؟</h3>
                        <p>يوفر التطبيق أكثر من 50 نموذجاً محاكياً لجميع اختبارات قياس.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- إضافة قسم النشرة البريدية -->
        <!-- <section class="newsletter" id="newsletter">
            <div class="container">
                <div class="newsletter-container">
                    <div class="newsletter-content">
                        <h2 class="newsletter-title">احصل على آخر التحديثات</h2>
                        <p class="newsletter-subtitle">اشترك في نشرتنا البريدية للحصول على آخر تحديثات اختبارات قياس ونصائح للتفوق</p>
                        <form class="newsletter-form" id="newsletter-form" action="https://formspree.io/f/your_formspree_id" method="POST">
                            <div class="form-group">
                                <input type="email" name="email" id="email" placeholder="أدخل بريدك الإلكتروني" required>
                                <button type="submit" class="subscribe-btn">اشترك الآن</button>
                            </div>
                            <div class="form-privacy">
                                <label>
                                    <input type="checkbox" name="consent" required>
                                    <span>أوافق على <a href="privacy-policy.html">سياسة الخصوصية</a> وتلقي رسائل بريدية من مجتهد</span>
                                </label>
                            </div>
                            <input type="hidden" name="_subject" value="اشتراك جديد في النشرة البريدية - مجتهد">
                            <input type="hidden" name="_next" value="https://mujtahidacademy.com/thank-you.html">
                        </form>
                    </div>
                    <div class="newsletter-image">
                        <img src="images/logo1.png" alt="نشرة مجتهد البريدية" width="150" height="150">
                    </div>
                </div>
            </div>
        </section> -->
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="images/logo1.png" alt="شعار مجتهد">
                    <p>تطبيق مجتهد هو الحل الأمثل للطلاب الراغبين في التفوق في اختبارات قياس. يوفر التطبيق أكثر من 10,000 سؤال محلول وشرح تفصيلي لجميع اختبارات قياس في المملكة العربية السعودية.</p>
                </div>

                <div class="footer-links">
                    <h3 class="footer-title">محاكيات قياس</h3>
                    <a href="qudrat.html" class="footer-link">محاكي القدرات العامة</a>
                    <a href="tahsili.html" class="footer-link">محاكي التحصيلي</a>
                    <a href="step.html" class="footer-link">محاكي STEP</a>
                    <a href="cognitive.html" class="footer-link">محاكي القدرة المعرفية</a>
                    <a href="university.html" class="footer-link">محاكي قدرات الجامعيين</a>
                    <a href="license.html" class="footer-link">محاكي الرخصة المهنية</a>
                </div>

                <div class="footer-links">
                    <h3 class="footer-title">الدعم</h3>
                    <a href="privacy-policy.html" class="footer-link">سياسة الخصوصية</a>
                    <a href="terms.html" class="footer-link">شروط الاستخدام</a>
                    <a href="mailto:<EMAIL>" class="footer-link">المساعدة</a>
                    <a href="#faq" class="footer-link">الأسئلة الشائعة</a>
                </div>

                <div class="footer-contact">
                    <h3 class="footer-title">تواصل معنا</h3>
                    <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>0581105341</span>
                    </div>

                    <div class="social-links">
                        <a href="https://twitter.com/mujtahidacademy" class="social-link" target="_blank" rel="noopener" aria-label="تويتر">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="https://www.instagram.com/mujtahidacademy" class="social-link" target="_blank" rel="noopener" aria-label="انستغرام">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="https://www.tiktok.com/@mujtahidacademy" class="social-link" target="_blank" rel="noopener" aria-label="تيك توك">
                            <i class="fab fa-tiktok"></i>
                        </a>
                        <a href="https://www.youtube.com/channel/mujtahidacademy" class="social-link" target="_blank" rel="noopener" aria-label="يوتيوب">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- إضافة قسم المشاركة -->
            <div class="share-section">
                <h3 class="share-title">شارك التطبيق مع أصدقائك</h3>
                <p class="share-subtitle">ساعد زملائك في التفوق في اختبارات قياس</p>
                <div class="share-buttons">
                    <a href="https://www.facebook.com/sharer/sharer.php?u=https://mujtahidacademy.com" class="share-btn facebook" target="_blank" rel="noopener" aria-label="مشاركة على فيسبوك">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://twitter.com/intent/tweet?url=https://mujtahidacademy.com&text=تطبيق مجتهد - محاكي اختبارات قياس مع أكثر من 10,000 سؤال محلول" class="share-btn twitter" target="_blank" rel="noopener" aria-label="مشاركة على تويتر">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="https://api.whatsapp.com/send?text=تطبيق مجتهد - محاكي اختبارات قياس مع أكثر من 10,000 سؤال محلول https://mujtahidacademy.com" class="share-btn whatsapp" target="_blank" rel="noopener" aria-label="مشاركة على واتساب">
                        <i class="fab fa-whatsapp"></i>
                    </a>
                    <a href="https://t.me/share/url?url=https://mujtahidacademy.com&text=تطبيق مجتهد - محاكي اختبارات قياس مع أكثر من 10,000 سؤال محلول" class="share-btn telegram" target="_blank" rel="noopener" aria-label="مشاركة على تيليجرام">
                        <i class="fab fa-telegram-plane"></i>
                    </a>
                </div>
            </div>

            <div class="footer-bottom">
                <p>© 2023-2025 مجتهد - جميع الحقوق محفوظة | التطبيق الأول لتحضير اختبارات قياس في المملكة العربية السعودية</p>
            </div>
        </div>
    </footer>

    <!-- Main Script -->
    <script>
        // Twitter conversion tracking
        document.querySelectorAll('.download-btn').forEach(button => {
            button.addEventListener('click', function(e) {
                const isIOS = this.classList.contains('apple');
                const platform = isIOS ? 'iOS' : 'Android';

                twq('event', 'tw-pa23z-pa240', {
                    value: 1.00,
                    currency: 'SAR',
                    contents: [{
                        'content_id': isIOS ? 'ios_app_download' : 'android_app_download',
                        'content_type': 'app_download',
                        'platform': platform
                    }],
                    conversion_id: `${Date.now()}_${platform}`,
                    download_platform: platform,
                    event_time: new Date().toISOString()
                });

                console.log(`تم النقر على تحميل ${platform}`);
            });
        });

        // Animate stats on scroll
        function isInViewport(element) {
            const rect = element.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        }

        function animateStatsIfVisible() {
            const stats = document.querySelectorAll('.stat-item');
            stats.forEach(stat => {
                if (isInViewport(stat)) {
                    stat.classList.add('animate');
                }
            });
        }

        // Check on load and scroll
        window.addEventListener('load', animateStatsIfVisible);
        window.addEventListener('scroll', animateStatsIfVisible);

        // تحسين كود الأنيميشن مع requestIdleCallback
        function scheduleWork(callback) {
            if ('requestIdleCallback' in window) {
                requestIdleCallback(callback, { timeout: 1000 });
            } else {
                setTimeout(callback, 1);
            }
        }

        const observerOptions = {
            root: null,
            rootMargin: '50px',
            threshold: 0.1
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    scheduleWork(() => {
                        entry.target.classList.add('animate');
                        observer.unobserve(entry.target);
                    });
                }
            });
        }, observerOptions);

        // مراقبة عناصر الإحصائيات
        document.addEventListener('DOMContentLoaded', () => {
            scheduleWork(() => {
                const stats = document.querySelectorAll('.stat-item');
                stats.forEach(stat => observer.observe(stat));
            });

            // إزالة أي event listeners قد تؤثر على السكرول
            window.removeEventListener('scroll', animateStatsIfVisible);
        });

        // تحسين أداء السكرول في الأجهزة المحمولة
        let touchStartY = 0;
        document.addEventListener('touchstart', (e) => {
            touchStartY = e.touches[0].clientY;
        }, { passive: true });

        document.addEventListener('touchmove', (e) => {
            const touchY = e.touches[0].clientY;
            const scrolled = touchStartY - touchY;

            if (Math.abs(scrolled) > 5) {
                e.stopPropagation();
            }
        }, { passive: true });
    </script>

    <!-- تحديث JavaScript في نهاية body -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // إزالة جميع event listeners القديمة
            window.removeEventListener('scroll', animateStatsIfVisible);

            // استخدام Intersection Observer للأنيميشن
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });

            // تطبيق المراقبة على العناصر
            document.querySelectorAll('.stat-item').forEach(item => {
                observer.observe(item);
            });

            // تحسين أداء السكرول للموبايل
            let isTouching = false;
            let lastY = 0;

            document.addEventListener('touchstart', (e) => {
                isTouching = true;
                lastY = e.touches[0].clientY;
            }, { passive: true });

            document.addEventListener('touchend', () => {
                isTouching = false;
            }, { passive: true });

            // التعامل مع السكرول بشكل أكثر كفاءة
            let scrollTimeout;
            window.addEventListener('scroll', () => {
                if (scrollTimeout) {
                    window.cancelAnimationFrame(scrollTimeout);
                }

                scrollTimeout = window.requestAnimationFrame(() => {
                    // تحديث UI بعد توقف السكرول
                });
            }, { passive: true });
        });

        // تبسيط كود تتبع التحميل
        document.querySelectorAll('.download-btn').forEach(button => {
            button.addEventListener('click', function(e) {
                const isIOS = this.classList.contains('apple');
                trackDownload(isIOS ? 'iOS' : 'Android');
            }, { passive: true });
        });

        function trackDownload(platform) {
            // تتبع التحميل
            if (typeof twq !== 'undefined') {
                twq('event', 'tw-pa23z-pa240', {
                    value: 1.00,
                    currency: 'SAR',
                    contents: [{
                        content_id: `${platform.toLowerCase()}_app_download`,
                        content_type: 'app_download',
                        platform: platform
                    }]
                });
            }

            if (typeof gtag !== 'undefined') {
                gtag('event', 'conversion', {
                    'send_to': 'AW-16930945793/app_download',
                    'value': 1.0,
                    'currency': 'SAR',
                    'transaction_id': `${Date.now()}_${platform}`
                });
            }
        }

        // وظيفة الاشتراك في النشرة البريدية
        function subscribeNewsletter(event) {
            event.preventDefault();

            const email = document.getElementById('email').value;
            if (!email) return false;

            // تتبع الاشتراك في النشرة البريدية
            if (typeof gtag !== 'undefined') {
                gtag('event', 'newsletter_signup', {
                    'event_category': 'engagement',
                    'event_label': 'newsletter'
                });
            }

            // هنا يمكن إضافة كود لإرسال البريد الإلكتروني إلى خادم API
            // في هذا المثال، سنعرض رسالة نجاح فقط

            // عرض رسالة نجاح
            const form = document.getElementById('newsletter-form');
            form.innerHTML = '<div class="success-message">شكراً لاشتراكك! سنرسل لك آخر التحديثات قريباً.</div>';

            return false;
        }

        // تتبع مشاركات المستخدمين
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تتبع لأزرار المشاركة
            document.querySelectorAll('.share-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    const platform = this.classList.contains('facebook') ? 'Facebook' :
                                    this.classList.contains('twitter') ? 'Twitter' :
                                    this.classList.contains('whatsapp') ? 'WhatsApp' :
                                    this.classList.contains('telegram') ? 'Telegram' : 'Other';

                    // تتبع المشاركة في Google Analytics
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'share', {
                            'method': platform,
                            'content_type': 'website',
                            'item_id': 'mujtahid_website'
                        });
                    }

                    console.log(`تمت المشاركة عبر ${platform}`);
                });
            });

            // تتبع الوصول للأقسام المختلفة
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const sectionId = entry.target.id;
                        if (sectionId && typeof gtag !== 'undefined') {
                            gtag('event', 'view_section', {
                                'section_id': sectionId,
                                'section_name': getSectionName(sectionId)
                            });
                        }
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.5
            });

            // مراقبة الأقسام الرئيسية
            document.querySelectorAll('section[id]').forEach(section => {
                observer.observe(section);
            });

            // الحصول على اسم القسم
            function getSectionName(id) {
                switch(id) {
                    case 'features': return 'مميزات التطبيق';
                    case 'screenshots': return 'صور التطبيق';
                    case 'reviews': return 'آراء المستخدمين';
                    case 'faq': return 'الأسئلة الشائعة';
                    default: return id;
                }
            }
        });

        // Register Service Worker for performance
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('SW registered successfully');
                    })
                    .catch(function(error) {
                        console.log('SW registration failed');
                    });
            });
        }
    </script>
</body>
</html>
