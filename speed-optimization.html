<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Critical CSS inline -->
    <style>
        /* Critical CSS for above-the-fold content */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            background: #f8f9fa;
        }
        
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 20px;
            text-align: center;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: #ff6b6b;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
    </style>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    
    <!-- Fallback for browsers that don't support preload -->
    <noscript>
        <link rel="stylesheet" href="styles.css">
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap">
    </noscript>
</head>
<body>
    <!-- Critical above-the-fold content -->
    <section class="hero">
        <div class="container">
            <h1>تحسين سرعة موقع مجتهد</h1>
            <p>تطبيق أفضل الممارسات لتحسين أداء الموقع</p>
            <a href="#optimizations" class="btn">استكشف التحسينات</a>
        </div>
    </section>

    <!-- Lazy load non-critical content -->
    <div id="content" style="display: none;">
        <section id="optimizations" class="optimizations">
            <div class="container">
                <h2>التحسينات المطبقة</h2>
                
                <div class="optimization-grid">
                    <div class="optimization-item">
                        <h3>🚀 تحسين تحميل الخطوط</h3>
                        <ul>
                            <li>استخدام font-display: swap</li>
                            <li>Preload للخطوط المهمة</li>
                            <li>تقليل عدد الخطوط المحملة</li>
                        </ul>
                    </div>
                    
                    <div class="optimization-item">
                        <h3>📱 تحسين الصور</h3>
                        <ul>
                            <li>Lazy loading للصور</li>
                            <li>تحديد أبعاد الصور</li>
                            <li>استخدام WebP عند الإمكان</li>
                            <li>ضغط الصور</li>
                        </ul>
                    </div>
                    
                    <div class="optimization-item">
                        <h3>⚡ تحسين CSS</h3>
                        <ul>
                            <li>Critical CSS inline</li>
                            <li>تأجيل تحميل CSS غير المهم</li>
                            <li>تصغير ملفات CSS</li>
                            <li>إزالة CSS غير المستخدم</li>
                        </ul>
                    </div>
                    
                    <div class="optimization-item">
                        <h3>🔧 تحسين JavaScript</h3>
                        <ul>
                            <li>تأجيل تحميل JS غير المهم</li>
                            <li>استخدام requestIdleCallback</li>
                            <li>تحسين Event Listeners</li>
                            <li>Code splitting</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Load non-critical CSS asynchronously -->
    <script>
        // Load CSS asynchronously
        function loadCSS(href) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            document.head.appendChild(link);
        }

        // Load non-critical content after page load
        window.addEventListener('load', function() {
            // Show hidden content
            document.getElementById('content').style.display = 'block';
            
            // Load additional CSS
            loadCSS('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css');
            
            // Initialize lazy loading for images
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            observer.unobserve(img);
                        }
                    });
                });

                document.querySelectorAll('img[data-src]').forEach(img => {
                    imageObserver.observe(img);
                });
            }
        });

        // Service Worker for caching
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>

    <!-- Additional optimizations -->
    <style>
        /* Non-critical CSS loaded after page load */
        .optimizations {
            padding: 80px 0;
            background: white;
        }
        
        .optimization-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .optimization-item {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .optimization-item h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .optimization-item ul {
            list-style: none;
            padding: 0;
        }
        
        .optimization-item li {
            padding: 5px 0;
            position: relative;
            padding-left: 20px;
        }
        
        .optimization-item li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        
        /* Lazy loading styles */
        .lazy {
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .lazy.loaded {
            opacity: 1;
        }
        
        /* Performance optimizations */
        * {
            box-sizing: border-box;
        }
        
        img {
            max-width: 100%;
            height: auto;
        }
        
        /* Reduce layout shifts */
        .hero {
            contain: layout style paint;
        }
        
        /* Optimize animations */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
        
        /* Critical resource hints */
        .preload-hint {
            display: none;
        }
    </style>
</body>
</html>
