<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#0c73e9">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    
    <title>الصفحة غير موجودة | مجتهد</title>
    <meta name="description" content="عذراً، الصفحة التي تبحث عنها غير موجودة. يمكنك العودة للصفحة الرئيسية والاستمرار في تصفح موقع مجتهد لاختبارات قياس.">
    <meta name="robots" content="noindex, follow">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://www.mujtahidacademy.com/404.html">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/favicon/favicon.ico">
    <link rel="icon" type="image/svg+xml" href="images/favicon/favicon.svg">
    <link rel="icon" type="image/png" sizes="96x96" href="images/favicon/favicon-96x96.png">
    <link rel="apple-touch-icon" href="images/favicon/apple-touch-icon.png">
    <link rel="manifest" href="site.webmanifest">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">
    
    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 40px 20px;
            background: var(--background);
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: 800;
            background: var(--modern-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
            line-height: 1;
        }
        
        .error-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text);
            margin-bottom: 20px;
        }
        
        .error-message {
            font-size: 1.2rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto 40px;
            line-height: 1.6;
        }
        
        .home-button {
            display: inline-block;
            background: var(--modern-gradient);
            color: var(--white);
            padding: 15px 30px;
            border-radius: var(--border-radius-md);
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-md);
        }
        
        .home-button:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }
        
        .error-image {
            max-width: 400px;
            margin-bottom: 40px;
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 6rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-message {
                font-size: 1.1rem;
            }
            
            .error-image {
                max-width: 300px;
            }
        }
        
        @media (max-width: 480px) {
            .error-code {
                font-size: 4rem;
            }
            
            .error-title {
                font-size: 1.8rem;
            }
            
            .error-image {
                max-width: 250px;
            }
        }
    </style>
</head>
<body>
    <main class="error-page">
        <div class="logo">
            <img src="images/logo1.png" alt="شعار تطبيق مجتهد" style="height: 60px; margin-bottom: 40px;">
        </div>
        
        <div class="error-code">404</div>
        <h1 class="error-title">الصفحة غير موجودة</h1>
        <p class="error-message">عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها. يمكنك العودة للصفحة الرئيسية والاستمرار في تصفح موقع مجتهد لاختبارات قياس.</p>
        
        <a href="/" class="home-button">العودة للصفحة الرئيسية</a>
    </main>
    
    <script>
        // تتبع خطأ 404 في Google Analytics
        if (typeof gtag === 'function') {
            gtag('event', 'page_view', {
                page_title: '404 - الصفحة غير موجودة',
                page_location: window.location.href,
                page_path: window.location.pathname,
                send_to: 'AW-16930945793'
            });
        }
    </script>
</body>
</html>
