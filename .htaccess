# تحسين أداء الموقع وتصدر محركات البحث
# Enable GZIP compression
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# Set browser caching
<IfModule mod_expires.c>
  ExpiresActive On
  ExpiresByType image/jpg "access plus 1 year"
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/gif "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/svg+xml "access plus 1 month"
  ExpiresByType text/css "access plus 1 month"
  ExpiresByType text/javascript "access plus 1 month"
  ExpiresByType application/javascript "access plus 1 month"
  ExpiresByType application/x-javascript "access plus 1 month"
  ExpiresByType application/json "access plus 1 week"
  ExpiresByType application/pdf "access plus 1 month"
  ExpiresByType application/x-shockwave-flash "access plus 1 month"
  ExpiresDefault "access plus 2 days"
</IfModule>

# Redirect non-www to www
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteCond %{HTTP_HOST} ^mujtahidacademy\.com [NC]
  RewriteRule ^(.*)$ https://www.mujtahidacademy.com/$1 [L,R=301]
</IfModule>

# Redirect HTTP to HTTPS
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteCond %{HTTPS} off
  RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# Prevent directory listing
Options -Indexes

# Prevent access to hidden files
<FilesMatch "^\.">
  Order allow,deny
  Deny from all
</FilesMatch>

# Custom error pages
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html

# Allow cross-origin resource sharing
<IfModule mod_headers.c>
  Header set Access-Control-Allow-Origin "*"
</IfModule>

# Disable ETags
<IfModule mod_headers.c>
  Header unset ETag
</IfModule>
FileETag None

# Prevent clickjacking
<IfModule mod_headers.c>
  Header set X-Frame-Options "SAMEORIGIN"
</IfModule>

# Prevent MIME type sniffing
<IfModule mod_headers.c>
  Header set X-Content-Type-Options "nosniff"
</IfModule>

# Enable XSS protection
<IfModule mod_headers.c>
  Header set X-XSS-Protection "1; mode=block"
</IfModule>

# Clean URLs
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteRule ^privacy-policy$ privacy-policy.html [L]
  RewriteRule ^terms$ terms.html [L]
</IfModule>
