/* Base Styles */
:root {
    --primary: #0c73e9;
    --primary-light: #4a90e2;
    --primary-dark: #0a5bba;
    --accent: #34d399;
    --text: #2c3e50;
    --text-light: #7f8c8d;
    --white: #ffffff;
    --background: #f8fafc;
    --background-alt: #eef2f7;
    --border: #e1e8ed;
    --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 5px 15px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.12);
    --shadow-glow: 0 10px 25px rgba(12, 115, 233, 0.2);
    --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    --font-primary: '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', sans-serif;
    --font-secondary: '<PERSON><PERSON><PERSON>', sans-serif;
    --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --border-radius-lg: 20px;
    --border-radius-md: 15px;
    --border-radius-sm: 10px;
    --glass-bg: rgba(255, 255, 255, 0.7);
    --glass-border: rgba(255, 255, 255, 0.5);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --modern-gradient: linear-gradient(135deg, #0c73e9 0%, #2563eb 50%, #4f46e5 100%);
    --accent-gradient: linear-gradient(135deg, #34d399 0%, #10b981 100%);
    --card-shadow: 
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    letter-spacing: -0.2px;
    line-height: 1.6;
    color: var(--text);
    background: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

img {
    max-width: 100%;
    height: auto;
}

/* Hero Section - تحديث */
.hero {
    min-height: 100vh;
    padding: 120px 0 60px; /* زيادة padding-top */
    height: auto;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; /* تحسين السكرول على iOS */
    background: var(--background);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.hero::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: radial-gradient(circle at top right, rgba(12, 115, 233, 0.1) 0%, transparent 60%),
                radial-gradient(circle at bottom left, rgba(79, 70, 229, 0.1) 0%, transparent 60%);
    z-index: 0;
}

.logo {
    position: absolute;
    top: 30px;
    right: 40px;
    z-index: 10;
}

.logo img {
    height: 50px;
    width: auto;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-text {
    animation: fadeIn 1s ease;
}

.hero-text h1 {
    font-family: var(--font-primary);
    font-size: 4.2rem;
    font-weight: 800;
    line-height: 1.2;
    background: var(--modern-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient 8s ease infinite;
    background-size: 200% 200%;
    text-align: right;
    margin-bottom: 25px;
    position: relative;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.hero-text h2 {
    font-family: var(--font-secondary);
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text);
    margin-bottom: 25px;
    line-height: 1.4;
}

.hero-text p {
    font-family: var(--font-secondary);
    font-size: 1.25rem;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 35px;
    max-width: 90%;
}

/* Download Buttons - تصميم جديد أكثر احترافية */
.download-buttons {
    display: flex;
    gap: 20px;
    margin: 40px 0;
}

/* تحسين أزرار التحميل */
.download-buttons {
    display: flex;
    gap: 20px;
    margin: 40px auto;
    justify-content: center;
    flex-wrap: wrap; /* إضافة خاصية الالتفاف */
    max-width: 100%; /* تحديد العرض الأقصى */
}

.download-btn {
    display: flex;
    align-items: center;
    padding: 18px 35px;
    border-radius: var(--border-radius-md);
    transition: var(--transition-smooth);
    backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    gap: 12px;
    color: var(--white);
    font-weight: 700;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.download-btn {
    flex: 1;
    min-width: 200px; /* الحد الأدنى للعرض */
    max-width: 280px; /* الحد الأقصى للعرض */
    padding: 15px 25px;
}

.download-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.5s ease;
    z-index: -1;
}

.download-btn:hover::before {
    transform: scaleX(1);
    transform-origin: left;
}

.download-btn.apple {
    background: linear-gradient(135deg, #000000 0%, #333333 100%);
}

.download-btn.google {
    background: var(--modern-gradient);
}

.download-btn:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.download-btn i {
    font-size: 2.5rem;
    margin-left: 15px;
}

.download-btn span {
    display: flex;
    flex-direction: column;
}

.download-btn small {
    font-size: 0.8rem;
    opacity: 0.9;
}

.download-btn strong {
    font-size: 1.3rem;
    margin-top: 2px;
}

/* Stats Component */
.stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
    margin-top: 40px;
    width: 100%;
}

/* Stats Component - تحديث */
.stat-item {
    text-align: center;
    background: var(--glass-bg);
    border-radius: var(--border-radius-lg);
    padding: 30px 20px;
    box-shadow: var(--glass-shadow);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(12px);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
    min-width: 150px;
    min-height: 120px; /* إضافة ارتفاع ثابت */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transform-style: preserve-3d;
    perspective: 1000px;
}

.stat-item:hover {
    transform: translateY(-5px) rotateX(5deg);
    box-shadow: 
        0 20px 40px rgba(12, 115, 233, 0.15),
        0 0 20px rgba(12, 115, 233, 0.1);
}

.stat-value {
    font-size: 3.5rem;
    font-weight: 800;
    background: var(--modern-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 12px;
    line-height: 1.2;
    display: block;
    line-height: 1;
    margin-bottom: 15px;
    position: relative;
}

.stat-label {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text);
    display: block;
}

/* تحسين الريسبونسف للإحصائيات */
@media (max-width: 768px) {
    .stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin: 30px auto;
    }
    
    .stat-value {
        font-size: 2.5rem;
    }

    .stat-item {
        min-height: 100px;
    }

    .feature-card:hover {
        transform: translateY(-5px) scale(1.01);
    }
    
    .stat-item:hover {
        transform: translateY(-3px);
    }
}

@media (max-width: 480px) {
    .stats {
        grid-template-columns: 1fr;
        max-width: 280px;
    }
    
    .stat-item {
        padding: 25px 20px;
    }
    
    .stat-value {
        font-size: 2.2rem;
    }
    
    .stat-label {
        font-size: 1.1rem;
    }
}

/* App Image - تصميم محسن */
.hero-image {
    position: relative;
    animation: fadeInRight 1s ease;
}

.app-showcase {
    position: relative;
    animation: float-modern 6s ease-in-out infinite;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    min-height: 600px;
}

.app-preview {
    width: 100%;
    max-width: 360px;
    border-radius: var(--border-radius-lg);
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 30px rgba(12, 115, 233, 0.2);
    border: 15px solid rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(12px);
    transform: perspective(1000px) rotateY(-12deg) rotateX(5deg);
    transition: all 0.5s ease;
    z-index: 2;
    position: relative;
}

.app-preview:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg) scale(1.03);
    box-shadow: var(--shadow-glow), 0 25px 50px rgba(12, 115, 233, 0.25);
}

.image-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border-radius: 50px;
    background: radial-gradient(circle at center, var(--primary) 0%, transparent 70%);
    filter: blur(90px);
    opacity: 0.15;
    z-index: 1;
    animation: glow 4s ease-in-out infinite;
}

.app-badge {
    font-family: var(--font-primary);
    font-weight: 700;
    letter-spacing: 0.5px;
    padding: 18px;
    background: linear-gradient(135deg, var(--accent) 0%, #10b981 100%);
    color: var(--white);
    border-radius: 50%;
    font-weight: bold;
    font-size: 1rem;
    box-shadow: var(--shadow-md);
    z-index: 3;
    transform: rotate(15deg);
    animation: float 3s ease-in-out infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.1;
        transform: translate(-50%, -50%) scale(0.9);
    }
    100% {
        opacity: 0.2;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

/* Footer - تصميم جديد أنيق */
.footer {
    background: var(--white);
    padding: 80px 0 30px;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, var(--border), transparent);
}

.footer-content {
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr 1.5fr;
    gap: 60px;
    margin-bottom: 60px;
}

.footer-logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.footer-logo img {
    height: 45px;
    width: auto;
    margin-bottom: 20px;
    object-fit: contain;
    max-width: 180px;
}

.footer-logo p {
    color: var(--text-light);
    margin-bottom: 20px;
    line-height: 1.7;
    font-size: 0.95rem;
    opacity: 0.9;
}

.footer-title {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 25px;
    position: relative;
    display: inline-block;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    right: 0;
    width: 40px;
    height: 3px;
    background: var(--primary);
    border-radius: 3px;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.footer-link {
    font-family: var(--font-secondary);
    font-size: 1.1rem;
    color: var(--text);
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-block;
}

.footer-link:hover {
    color: var(--primary);
    transform: translateX(-10px);
}

.contact-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    color: var (--text);
}

.contact-info i {
    width: 40px;
    height: 40px;
    background: var(--background);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-left: 15px;
    font-size: 1.2rem;
}

.contact-info span {
    font-weight: 500;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.social-link {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: var(--background);
    color: var (--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.social-link:hover {
    background: var(--primary);
    color: var(--white);
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.footer-bottom {
    border-top: 1px solid var(--border);
    padding-top: 30px;
    text-align: center;
}

.footer-bottom p {
    color: var(--text-light);
    font-size: 0.95rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-15px) rotate(2deg);
    }
}

@keyframes glow {
    0%, 100% {
        opacity: 0.5;
        transform: scale(0.8);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

@keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes float-modern {
    0%, 100% {
        transform: translateY(0) rotate(0deg) scale(1);
    }
    50% {
        transform: translateY(-20px) rotate(2deg) scale(1.02);
    }
}

.image-glow {
    animation: glow 4s ease-in-out infinite;
}

/* Improved Responsive Design */
@media (max-width: 1200px) {
    .hero-text h1 {
        font-size: 3.8rem;
    }
    
    .hero-text h2 {
        font-size: 2.2rem;
    }

    .hero-content {
        gap: 40px;
        padding: 0 40px;
    }
    
    .stat-value {
        font-size: 2.2rem;
    }
}

@media (max-width: 992px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        max-width: 600px;
        text-align: center;
        padding: 0 20px;
    }
    
    .hero-text h1 {
        font-size: 3.5rem;
    }
    
    .hero-text h2 {
        font-size: 2rem;
    }
    
    .hero-text p {
        margin-right: auto;
        margin-left: auto;
    }
    
    .download-buttons {
        justify-content: center;
    }
    
    .stats {
        max-width: 600px;
        margin: 0 auto;
        justify-content: center;
        gap: 15px;
    }
    
    .hero-image {
        margin: 40px 0;
        display: flex;
        justify-content: center;
    }
    
    .app-preview {
        transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
    }
    
    .app-showcase {
        min-height: auto;
        justify-content: center;
        margin-top: 40px;
        order: 2;
        margin: 40px auto;
        max-width: 500px;
    }

    .hero-text {
        order: 1;
    }

    .stat-item {
        padding: 20px;
    }

    .hero-content {
        grid-template-columns: 1fr;
        padding: 0 20px;
        margin-top: 60px;
    }
    
    .screenshots-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .hero {
        padding: 60px 0;
    }
    
    .logo {
        top: 20px;
        right: 20px;
    }
    
    .logo img {
        height: 40px;
    }
    
    .hero-text h1 {
        font-size: 2.8rem;
    }
    
    .hero-text h2 {
        font-size: 1.8rem;
    }
    
    .hero-text p {
        font-size: 1.1rem;
    }
    
    .download-buttons {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
    
    .download-btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
    
    .stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .app-badge {
        top: -15px;
        right: -15px;
        padding: 10px;
        font-size: 0.9rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .hero-content {
        gap: 30px;
    }
    
    .app-showcase {
        margin-top: 20px;
    }

    .footer-logo {
        align-items: center;
        text-align: center;
    }
    
    .footer-logo img {
        height: 40px;
        margin-bottom: 15px;
    }

    .stat-value {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 1rem;
    }

    .stat-item {
        min-height: 100px;
    }

    .hero {
        padding-top: 100px;
    }

    .main-header {
        padding: 10px 0;
    }
    
    .screenshots-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .screenshot-card {
        aspect-ratio: 9/19;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }

    .stat-item {
        padding: 15px;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    .download-buttons {
        flex-direction: column;
        align-items: center;
        gap: 15px;
        padding: 0 20px;
    }

    .download-btn {
        width: 100%;
        min-width: unset;
        max-width: 100%;
    }
    
    .download-btn i {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 80px 0 40px;
    }
    
    .hero-text h1 {
        font-size: 2.2rem;
    }
    
    .hero-text h2 {
        font-size: 1.5rem;
    }
    
    .hero-text p {
        font-size: 1rem;
        margin-bottom: 30px;
    }
    
    .download-btn {
        padding: 15px 20px;
    }
    
    .download-btn i {
        font-size: 2rem;
    }
    
    .download-btn strong {
        font-size: 1.1rem;
    }
    
    .app-preview {
        max-width: 280px;
        border-width: 8px;
    }
    
    .stat-value {
        font-size: 1.8rem;
    }
    
    .stat-label {
        font-size: 1rem;
    }

    .stats {
        grid-template-columns: 1fr;
        max-width: 280px;
        margin: 30px auto;
    }

    .stat-item {
        padding: 16px;
    }

    .screenshots-grid {
        grid-template-columns: 1fr;
        max-width: 280px;
        margin: 20px auto;
    }

    .screenshot-card {
        aspect-ratio: 9/16;
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .download-buttons {
        padding: 0 15px;
    }

    .stats {
        padding: 0 15px;
    }

    .main-header .container {
        padding: 0 15px;
    }
}

@media (max-width: 360px) {
    .download-btn {
        padding: 12px 20px;
    }
    
    .download-btn i {
        font-size: 1.8rem;
    }
    
    .download-btn small {
        font-size: 0.75rem;
    }
    
    .download-btn strong {
        font-size: 1rem;
    }
}

/* Features Section */
.features-list {
    margin: 30px 0;
    text-align: right;
}

.features-list ul {
    list-style: none;
    padding: 0;
}

.features-list li {
    margin: 15px 0;
    padding-right: 30px;
    position: relative;
    font-size: 1.1rem;
    color: var(--text);
}

.features-list li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--accent);
    font-weight: bold;
}

.features {
    padding: 80px 0;
    background: var(--background-alt);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-card {
    background: var(--glass-bg);
    padding: 35px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-height: 250px; /* إضافة ارتفاع ثابت */
    display: flex;
    flex-direction: column;
    gap: 15px;
    backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
}

.feature-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 
        0 20px 40px rgba(12, 115, 233, 0.15),
        0 0 20px rgba(12, 115, 233, 0.1);
}

.feature-card.soon {
    background: linear-gradient(to bottom right, var(--background), var(--background-alt));
    border: 2px dashed var(--primary-light);
    opacity: 0.8;
}

.coming-soon-badge {
    position: absolute;
    top: 20px;
    left: 20px;
    background: var(--accent);
    color: var(--white);
    padding: 5px 15px;
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    font-weight: 600;
    transform: rotate(-15deg);
}

.feature-card .feature-icon {
    margin-bottom: 20px;
    background: var(--modern-gradient);
    transform: rotate(-5deg);
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-card:hover .feature-icon {
    transform: rotate(5deg) scale(1.1);
}

/* تحسين الريسبونسف للمميزات */
@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .feature-card {
        padding: 25px;
        min-height: 200px;
    }
}

@media (max-width: 480px) {
    .features-grid {
        grid-template-columns: 1fr;
    }
}

/* تحسينات جديدة للمميزات */
.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-title {
    font-size: 2.5rem;
    color: var(--primary);
    margin-bottom: 15px;
    font-weight: 800;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

.feature-card {
    background: var(--glass-bg);
    padding: 35px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 15px;
    backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--modern-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    transform: rotate(-5deg);
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-icon i {
    font-size: 1.8rem;
    color: var(--white);
}

.feature-card h3 {
    font-size: 1.4rem;
    color: var(--text);
    margin: 15px 0;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 10px 0;
}

.feature-list li {
    padding-right: 25px;
    position: relative;
    margin: 8px 0;
    font-size: 1rem;
    color: var(--text-light);
}

.feature-list li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--accent);
}

.feature-card.highlight {
    border: 2px solid var(--primary-light);
    background: linear-gradient(145deg, var(--glass-bg), rgba(255, 255, 255, 0.9));
    border: 2px solid transparent;
    background-clip: padding-box;
}

.feature-card.highlight:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-glow);
}

/* إصلاح نسبة الرضا */
.satisfaction {
    display: inline-block;
    width: 100%;
    text-align: center;
}

/* تحسين الريسبونسف */
@media (max-width: 768px) {
    .section-title {
        font-size: 2rem;
    }
    
    .feature-card {
        padding: 25px;
    }
    
    .feature-icon {
        width: 50px;
        height: 50px;
    }
    
    .feature-icon i {
        font-size: 1.5rem;
    }
}

/* إضافة أنماط الـ Header الجديد */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-logo {
    height: 40px;
    width: auto;
}

.header-cta {
    background: var(--modern-gradient);
    color: var(--white);
    padding: 10px 25px;
    border-radius: 30px;
    font-weight: 600;
    transition: transform 0.3s ease;
}

.header-cta:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* أنماط المراجعات */
.reviews {
    padding: 100px 0;
    background: var(--background);
}

.reviews-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.review-card {
    background: var(--glass-bg);
    padding: 30px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--glass-shadow);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(12px);
    transition: var(--transition-smooth);
}

.review-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-glow);
}

.review-rating {
    color: #ffd700;
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.review-text {
    font-size: 1.1rem;
    color: var(--text);
    margin-bottom: 20px;
    line-height: 1.7;
}

.review-author {
    color: var(--primary);
    font-weight: 600;
    font-size: 1.1rem;
}

/* أنماط الأسئلة الشائعة */
.faq {
    padding: 100px 0;
    background: var(--background-alt);
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.faq-item {
    background: var(--glass-bg);
    padding: 30px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--glass-shadow);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(12px);
    transition: var(--transition-smooth);
}

.faq-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-glow);
}

.faq-item h3 {
    color: var(--primary);
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.faq-item p {
    color: var(--text-light);
    line-height: 1.7;
}

/* تحسين الريسبونسف */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
    }
    
    .reviews-grid {
        grid-template-columns: 1fr;
    }
    
    .faq-grid {
        grid-template-columns: 1fr;
    }
}

/* App Screenshots Section */
.app-screenshots {
    padding: 120px 0;
    background: linear-gradient(180deg, var(--background) 0%, var(--background-alt) 100%);
    overflow: hidden;
}

.screenshots-wrapper {
    display: grid;
    grid-template-columns: 1.2fr 0.8fr;
    gap: 60px;
    align-items: center;
    margin-top: 60px;
}

.phone-frame {
    position: relative;
    width: 100%;
    max-width: 380px;
    margin: 0 auto;
    perspective: 1000px;
}

.frame {
    width: 100%;
    height: auto;
    position: relative;
    z-index: 2;
}

.screenshot-slider {
    position: absolute;
    top: 2%;
    left: 4%;
    width: 92%;
    height: 96%;
    border-radius: 35px;
    overflow: hidden;
    background: var(--background);
}

.screenshot-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease;
    animation: slideScreenshots 15s infinite;
}

.screenshot-item:nth-child(1) { animation-delay: 0s; }
.screenshot-item:nth-child(2) { animation-delay: 5s; }
.screenshot-item:nth-child(3) { animation-delay: 10s; }

.screenshot-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 32px;
}

.features-highlights {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
}

.highlight-item {
    background: var(--glass-bg);
    padding: 30px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--glass-shadow);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(12px);
    transition: var(--transition-smooth);
    text-align: center;
}

.highlight-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-glow);
}

.highlight-icon {
    width: 70px;
    height: 70px;
    background: var(--modern-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.highlight-icon i {
    font-size: 2rem;
    color: var(--white);
}

.highlight-item h3 {
    font-size: 1.3rem;
    color: var(--text);
    margin-bottom: 10px;
}

.highlight-item p {
    color: var(--text-light);
    font-size: 1rem;
}

@keyframes slideScreenshots {
    0%, 28% {
        opacity: 1;
        transform: translateX(0);
    }
    33%, 95% {
        opacity: 0;
        transform: translateX(-100%);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تحسين الريسبونسف */
@media (max-width: 992px) {
    .screenshots-wrapper {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .phone-frame {
        max-width: 320px;
    }
}

@media (max-width: 768px) {
    .features-highlights {
        grid-template-columns: 1fr;
        max-width: 400px;
        margin: 0 auto;
    }
}

/* App Screenshots Section - تحديث */
.app-screenshots {
    padding: 120px 0;
    background: linear-gradient(180deg, var(--background) 0%, var(--background-alt) 100%);
    overflow: hidden;
}

.screenshots-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.screenshot-card {
    position: relative;
    border-radius: 25px;
    overflow: hidden;
    background: var(--white);
    box-shadow: var(--shadow-lg);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    aspect-ratio: 9/16;
}

.screenshot-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-glow),
                0 20px 40px rgba(12, 115, 233, 0.2);
}

.screenshot-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.screenshot-card:hover .screenshot-image {
    transform: scale(1.05);
}

.screenshot-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    color: var(--white);
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.screenshot-card:hover .screenshot-overlay {
    opacity: 1;
    transform: translateY(0);
}

.screenshot-overlay h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

/* تحسين الريسبونسف */
@media (max-width: 768px) {
    .screenshots-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 480px) {
    .screenshots-grid {
        grid-template-columns: 1fr;
        max-width: 300px;
        margin: 30px auto;
    }
}

/* تحسين الريسبونسف للشاشات المتوسطة */
@media (max-width: 992px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 30px;
        padding-top: 80px;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .screenshots-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* تحسين الريسبونسف للموبايل */
@media (max-width: 576px) {
    .hero {
        min-height: auto;
        padding: 100px 0 50px;
    }
    
    .hero-text h1 {
        font-size: 2rem;
        text-align: center;
    }

    .features-grid,
    .screenshots-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .download-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .stat-item {
        padding: 15px;
        min-height: auto;
    }
}

/* تحسين للشاشات الصغيرة جداً */
@media (max-width: 360px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-text h1 {
        font-size: 1.8rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
}

/* حل مشكلة النصوص الطويلة */
@media (max-width: 768px) {
    .review-text,
    .feature-card p {
        font-size: 0.95rem;
        line-height: 1.6;
    }
    
    .stat-value {
        font-size: 2rem;
    }
}

/* تحسين العرض على الأيفون */
.hero {
    min-height: 100vh;
    padding: 120px 0 60px; /* زيادة padding-top */
    height: auto;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; /* تحسين السكرول على iOS */
}

@media screen and (max-width: 768px) {
    .hero {
        min-height: auto;
        padding: 100px 0 40px;
    }

    .hero-content {
        margin-top: 0;
        padding-top: 0;
    }
    
    /* تحسين حجم العناصر */
    .hero-text h1 {
        font-size: 2rem;
        margin-bottom: 15px;
    }
    
    .app-preview {
        max-width: 280px;
        margin: 20px auto;
    }
}

/* تحسين خاص للأيفون */
@supports (-webkit-touch-callout: none) {
    .hero {
        min-height: -webkit-fill-available;
    }
    
    .hero-content {
        padding-bottom: 50px;
    }
}
