<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta name="theme-color" content="#0c73e9">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">

    <!-- تحسين عناوين الصفحة لـ SEO - محاكي القدرات العامة -->
    <title>محاكي اختبار القدرات المحوسب 2025 | أسئلة القدرات المحاكية | المركز الوطني للقياس</title>
    <meta name="description" content="محاكي اختبار القدرات المحوسب 2025 ✓ تدرب على 5,000+ سؤال محلول ✓ نماذج قياس محاكية 100% ✓ القسم الكمي والقسم اللفظي ✓ تحضير كامل لاختبار القدرات ✓ نماذج المركز الوطني للقياس ✓ شرح تفصيلي مجاني">
    <meta name="keywords" content="محاكي القدرات المحوسب, اختبار القدرات 2025, القسم الكمي, القسم اللفظي, تدريب قدرات, نماذج قياس محلولة, قياس كمي, قياس لفظي, اختبار قدرات تجريبي, المركز الوطني للقياس, قدرات محوسب, نماذج قدرات, قدرات ورقي, استعداد قياس, المركز الوطني للقياس السعودية, قدرات جامعيين, الاختبار التحصيلي, استعداد القدرات, قدرات عامة, قدرات كمي, قدرات لفظي">

    <!-- تحسين Meta Tags للـ SEO -->
    <meta name="language" content="Arabic">
    <meta name="subject" content="اختبار القدرات المحوسب - المركز الوطني للقياس">
    <meta name="copyright" content="مجتهد - Mujtahid 2025">
    <meta name="revised" content="Monday, April 17, 2025">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">

    <!-- تحسين Schema Markup لمحاكي اختبار القدرات -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@graph": [
        {
          "@type": "WebPage",
          "@id": "https://mujtahidacademy.com/qudrat/#webpage",
          "url": "https://mujtahidacademy.com/qudrat",
          "name": "محاكي اختبار القدرات المحوسب 2025 | أسئلة القدرات المحاكية | المركز الوطني للقياس",
          "isPartOf": {
            "@id": "https://mujtahidacademy.com/#website"
          },
          "description": "محاكي اختبار القدرات المحوسب 2025 - محاكاة دقيقة 100% لنماذج المركز الوطني للقياس بقسميه الكمي واللفظي. تدرب الآن على أكثر من 5,000 سؤال قدرات محلول مع شرح تفصيلي",
          "breadcrumb": {
            "@id": "https://mujtahidacademy.com/qudrat/#breadcrumb"
          },
          "primaryImageOfPage": {
            "@id": "https://mujtahidacademy.com/qudrat/#primaryimage"
          },
          "datePublished": "2025-04-17T08:00:00+03:00",
          "dateModified": "2025-04-17T08:00:00+03:00",
          "inLanguage": "ar"
        },
        {
          "@type": "BreadcrumbList",
          "@id": "https://mujtahidacademy.com/qudrat/#breadcrumb",
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "name": "الرئيسية",
              "item": "https://mujtahidacademy.com"
            },
            {
              "@type": "ListItem",
              "position": 2,
              "name": "محاكي اختبار القدرات المحوسب 2025"
            }
          ]
        },
        {
          "@type": "ImageObject",
          "@id": "https://mujtahidacademy.com/qudrat/#primaryimage",
          "inLanguage": "ar",
          "url": "https://mujtahidacademy.com/images/quiz.PNG",
          "width": 1280,
          "height": 720,
          "caption": "محاكي اختبار القدرات المحوسب 2025"
        },
        {
          "@type": "SoftwareApplication",
          "name": "محاكي اختبار القدرات المحوسب - مجتهد",
          "operatingSystem": ["iOS", "Android"],
          "applicationCategory": "EducationalApplication",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "SAR"
          },
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "ratingCount": "32500",
            "bestRating": "5",
            "worstRating": "1"
          }
        },
        {
          "@type": "FAQPage",
          "mainEntity": [
            {
              "@type": "Question",
              "name": "كيف أتدرب على اختبار القدرات المحوسب 2025؟",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "يمكنك التدرب على اختبار القدرات المحوسب 2025 من خلال تطبيق محاكي مجتهد الذي يوفر أكثر من 5,000 سؤال قدرات محلول مع شرح تفصيلي ومحاكاة دقيقة 100% لاختبار قياس الرسمي"
              }
            },
            {
              "@type": "Question",
              "name": "ما هي أقسام اختبار القدرات العامة؟",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "ينقسم اختبار القدرات العامة إلى قسمين رئيسيين: القسم الكمي ويشمل الرياضيات والمقارنات والاستنتاج الرياضي، والقسم اللفظي ويشمل المفردات والتناظر اللفظي والاستيعاب القرائي"
              }
            },
            {
              "@type": "Question",
              "name": "هل توجد أسئلة جديدة في اختبار القدرات 2025؟",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "نعم، يتضمن اختبار القدرات 2025 أنماطًا جديدة من الأسئلة، وتطبيق مجتهد يوفر محاكاة محدثة لهذه الأسئلة الجديدة مع شرح تفصيلي وتدريب على جميع أنماط الأسئلة المتوقعة"
              }
            },
            {
              "@type": "Question",
              "name": "ما المدة المتاحة لحل اختبار القدرات المحوسب؟",
              "acceptedAnswer": {
                "@type": "Answer",
                "text": "مدة اختبار القدرات المحوسب الرسمي هي ساعتان ونصف (150 دقيقة) موزعة على القسم الكمي والقسم اللفظي، ومحاكي مجتهد يحاكي نفس الزمن الرسمي لتجربة واقعية"
              }
            }
          ]
        }
      ]
    }
    </script>

    <!-- تحسين Open Graph Meta Tags -->
    <meta property="og:title" content="محاكي اختبار القدرات المحوسب 2025 | تدرب الآن مجاناً">
    <meta property="og:description" content="تدرب على اختبار القدرات المحوسب 2025 مع أكثر من 5,000 سؤال محلول. شرح تفصيلي واختبارات تجريبية مجانية مطابقة للمركز الوطني للقياس">
    <meta property="og:image" content="https://mujtahidacademy.com/images/quiz.PNG">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:url" content="https://mujtahidacademy.com/qudrat">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="أكاديمية مجتهد">
    <meta property="og:locale" content="ar_SA">

    <!-- تحسين Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@mujtahidacademy">
    <meta name="twitter:creator" content="@mujtahidacademy">
    <meta name="twitter:title" content="محاكي اختبار القدرات المحوسب 2025 | المركز الوطني للقياس">
    <meta name="twitter:description" content="تدرب على القدرات المحوسب مع أكثر من 5,000 سؤال محلول. محاكي قياس مجاني 100% مع شرح تفصيلي للقسم الكمي واللفظي">
    <meta name="twitter:image" content="https://mujtahidacademy.com/images/quiz.PNG">
    <meta name="twitter:image:alt" content="محاكي اختبار القدرات المحوسب 2025 - مجتهد">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://mujtahidacademy.com/qudrat">

    <!-- تضمين ملفات CSS والخطوط -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@400;500;700;900&family=Tajawal:wght@400;500;700;900&display=swap" rel="stylesheet">

    <!-- تحسين Favicon -->
    <link rel="icon" type="image/x-icon" href="images/favicon/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="images/favicon/apple-touch-icon.png">
</head>

<body>
    <!-- شريط علوي جديد -->
    <div class="top-bar">
        محاكاة حقيقية 100% لاختبار القدرات المحوسب 2025
        <span class="animated-icon">🎯</span>
        محدّث لنماذج المركز الوطني للقياس
    </div>

    <main>
        <!-- قسم القدرات المحوسب -->
        <section class="hero qudrat-hero">
            <div class="logo">
                <a href="index.html">
                    <img src="images/logo1.png" alt="شعار تطبيق مجتهد - محاكي اختبارات قياس 2025">
                </a>
            </div>

            <div class="hero-content">
                <div class="hero-text">
                    <h1>محاكي اختبار القدرات المحوسب 2025</h1>
                    <p class="hero-subtitle">🎯 تدرب الآن على أكثر من 5,000 سؤال محلول مع شرح تفصيلي واحصل على درجة عالية في اختبار قياس</p>

                    <div class="features-list">
                        <ul>
                            <li>محاكاة دقيقة 100% لنظام الاختبار المحوسب الرسمي من المركز الوطني للقياس</li>
                            <li>تغطية شاملة للقسم الكمي والقسم اللفظي بجميع أنماط الأسئلة</li>
                            <li>أسئلة مماثلة تماماً لأنماط وصعوبة المركز الوطني للقياس</li>
                            <li>شرح تفصيلي خطوة بخطوة لكل سؤال مع استراتيجيات الحل السريع</li>
                            <li>نماذج محاكية بنفس مستوى الصعوبة والوقت للاختبار الحقيقي</li>
                            <li>تتبع مستوى تقدمك وتحليل نقاط ضعفك مع خطة تحسين شخصية</li>
                        </ul>
                    </div>

                    <div class="cta-buttons">
                        <a href="#qudrat-sections" class="primary-btn">
                            <i class="fas fa-rocket"></i>
                            استكشف محتويات القدرات
                        </a>
                        <a href="#download-app" class="secondary-btn">
                            <i class="fas fa-download"></i>
                            تحميل التطبيق الآن
                        </a>
                    </div>
                </div>

                <div class="qudrat-image">
                    <img src="images/quiz.PNG" alt="محاكي اختبار القدرات المحوسب 2025" class="qudrat-preview">
                    <div class="badge-qudrat">🔥 محدث 2025</div>
                </div>
            </div>
        </section>

        <!-- أقسام القدرات المحوسب -->
        <section class="qudrat-sections" id="qudrat-sections">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">أقسام اختبار القدرات المحوسب</h2>
                    <p class="section-subtitle">تدرب على جميع أقسام اختبار القدرات العامة بالتفصيل</p>
                </div>

                <div class="qudrat-parts">
                    <div class="qudrat-part">
                        <div class="part-header">
                            <i class="fas fa-calculator"></i>
                            <h3>القسم الكمي - الرياضيات</h3>
                        </div>
                        <div class="part-content">
                            <ul class="part-list">
                                <li>الحساب والجبر والهندسة التحليلية</li>
                                <li>الإحصاء والاحتمالات والتحليل الإحصائي</li>
                                <li>التحليل العددي والاستدلال المنطقي</li>
                                <li>المقارنات الكمية والتناسب والنسب</li>
                                <li>الاستدلال الرياضي وحل المسائل الكلامية</li>
                                <li>الأشكال الهندسية والمساحات والحجوم</li>
                                <li>المعادلات والمتباينات والدوال</li>
                            </ul>
                            <p class="part-count">💎 أكثر من 2,500 سؤال محلول بالتفصيل</p>
                        </div>
                    </div>

                    <div class="qudrat-part">
                        <div class="part-header">
                            <i class="fas fa-book-open"></i>
                            <h3>القسم اللفظي - اللغة العربية</h3>
                        </div>
                        <div class="part-content">
                            <ul class="part-list">
                                <li>إكمال الجمل والمفردات اللغوية</li>
                                <li>التناظر اللفظي والعلاقات اللغوية</li>
                                <li>الاستيعاب القرائي وفهم النصوص</li>
                                <li>تحليل النصوص الأدبية والعلمية</li>
                                <li>الاستدلال اللغوي والمنطق اللفظي</li>
                                <li>الأخطاء الشائعة والقواعد النحوية</li>
                                <li>المعاني والمرادفات والأضداد</li>
                            </ul>
                            <p class="part-count">💎 أكثر من 2,500 سؤال محلول بالتفصيل</p>
                        </div>
                    </div>
                </div>

                <div class="qudrat-benefits">
                    <div class="benefit">
                        <div class="benefit-icon">
                            <i class="fas fa-stopwatch"></i>
                        </div>
                        <h4>محاكاة الوقت الحقيقي</h4>
                        <p>اختبار محاكي بنفس وقت الاختبار الرسمي (150 دقيقة) مع عداد تنازلي دقيق</p>
                    </div>

                    <div class="benefit">
                        <div class="benefit-icon">
                            <i class="fas fa-desktop"></i>
                        </div>
                        <h4>واجهة مطابقة 100%</h4>
                        <p>نفس واجهة الاختبار المحوسب الرسمي من المركز الوطني للقياس تماماً</p>
                    </div>

                    <div class="benefit">
                        <div class="benefit-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h4>تحليل الأداء المتقدم</h4>
                        <p>تقارير تفصيلية لمستوى أدائك مع تحديد نقاط القوة والضعف</p>
                    </div>

                    <div class="benefit">
                        <div class="benefit-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h4>استراتيجيات الحل السريع</h4>
                        <p>أفضل الاستراتيجيات والحيل لحل الأسئلة بسرعة ودقة عالية</p>
                    </div>

                    <div class="benefit">
                        <div class="benefit-icon">
                            <i class="fas fa-target"></i>
                        </div>
                        <h4>تدريب مخصص</h4>
                        <p>خطة تدريب شخصية مبنية على مستواك ونقاط ضعفك</p>
                    </div>

                    <div class="benefit">
                        <div class="benefit-icon">
                            <i class="fas fa-medal"></i>
                        </div>
                        <h4>ضمان التحسن</h4>
                        <p>نظام متابعة يضمن تحسن درجاتك مع كل اختبار تجريبي</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم الإحصائيات والنتائج -->
        <section class="qudrat-stats">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">نتائج مذهلة يحققها طلابنا</h2>
                    <p class="section-subtitle">أرقام حقيقية تثبت فعالية محاكي مجتهد في تحسين درجات الطلاب</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number">50,000+</div>
                        <div class="stat-label">طالب استفاد من التطبيق</div>
                        <div class="stat-description">طلاب حققوا نتائج ممتازة في اختبار القدرات</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-number">85%</div>
                        <div class="stat-label">معدل تحسن الدرجات</div>
                        <div class="stat-description">من الطلاب حسنوا درجاتهم بمعدل 20+ نقطة</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-number">4.8/5</div>
                        <div class="stat-label">تقييم التطبيق</div>
                        <div class="stat-description">تقييم عالي من المستخدمين على المتاجر</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number">2 أسابيع</div>
                        <div class="stat-label">متوسط فترة التحسن</div>
                        <div class="stat-description">الوقت المتوسط لملاحظة تحسن واضح في الدرجات</div>
                    </div>
                </div>

                <!-- شهادات الطلاب -->
                <div class="testimonials">
                    <h3 class="testimonials-title">ماذا يقول طلابنا؟</h3>
                    <div class="testimonials-grid">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <p>"بفضل تطبيق مجتهد، تحسنت درجتي في القدرات من 65 إلى 88! الشرح التفصيلي والنماذج المحاكية ساعدوني كثيراً."</p>
                            </div>
                            <div class="testimonial-author">
                                <div class="author-info">
                                    <h4>أحمد محمد</h4>
                                    <span>طالب ثانوي - الرياض</span>
                                </div>
                                <div class="author-score">
                                    <span class="score">88</span>
                                    <small>درجة القدرات</small>
                                </div>
                            </div>
                        </div>

                        <div class="testimonial">
                            <div class="testimonial-content">
                                <p>"التطبيق رائع جداً! المحاكاة مطابقة تماماً للاختبار الحقيقي. حصلت على 92 في القدرات والحمد لله."</p>
                            </div>
                            <div class="testimonial-author">
                                <div class="author-info">
                                    <h4>فاطمة العلي</h4>
                                    <span>طالبة ثانوي - جدة</span>
                                </div>
                                <div class="author-score">
                                    <span class="score">92</span>
                                    <small>درجة القدرات</small>
                                </div>
                            </div>
                        </div>

                        <div class="testimonial">
                            <div class="testimonial-content">
                                <p>"أفضل تطبيق للتحضير لاختبار القدرات. الأسئلة مشابهة جداً للاختبار الحقيقي والشرح واضح ومفصل."</p>
                            </div>
                            <div class="testimonial-author">
                                <div class="author-info">
                                    <h4>خالد السعد</h4>
                                    <span>طالب ثانوي - الدمام</span>
                                </div>
                                <div class="author-score">
                                    <span class="score">86</span>
                                    <small>درجة القدرات</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- تحميل التطبيق -->
        <section class="download-app" id="download-app">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">حمل تطبيق محاكي القدرات الآن</h2>
                    <p class="section-subtitle">تدرب في أي وقت وأي مكان - التطبيق متوفر مجاناً</p>
                </div>

                <div class="download-content">
                    <div class="app-info">
                        <div class="app-features">
                            <div class="app-feature">
                                <i class="fas fa-check-circle"></i>
                                <span>أكثر من 5,000 سؤال قدرات محلول بالتفصيل</span>
                            </div>
                            <div class="app-feature">
                                <i class="fas fa-check-circle"></i>
                                <span>نظام محاكاة طبق الأصل لاختبار المركز الوطني للقياس</span>
                            </div>
                            <div class="app-feature">
                                <i class="fas fa-check-circle"></i>
                                <span>محاكي لوضع الاختبار بنفس الوقت والأسئلة</span>
                            </div>
                            <div class="app-feature">
                                <i class="fas fa-check-circle"></i>
                                <span>شرح تفصيلي لجميع الأسئلة بأسلوب مبسط</span>
                            </div>
                            <div class="app-feature">
                                <i class="fas fa-check-circle"></i>
                                <span>تحديث مستمر لأنماط الأسئلة الجديدة</span>
                            </div>
                        </div>

                        <div class="download-buttons">
                            <a href="https://apps.apple.com/ae/app/مجتهد/id6605936809" class="download-btn apple" target="_blank" rel="noopener" aria-label="تحميل تطبيق مجتهد من App Store">
                                <i class="fab fa-apple"></i>
                                <span>
                                    <small>متوفر على</small>
                                    <strong>App Store</strong>
                                </span>
                            </a>

                            <a href="https://play.google.com/store/apps/details?id=com.mazen.flutterquiz" class="download-btn google" target="_blank" rel="noopener" aria-label="تحميل تطبيق مجتهد من Google Play">
                                <i class="fab fa-google-play"></i>
                                <span>
                                    <small>متوفر على</small>
                                    <strong>Google Play</strong>
                                </span>
                            </a>
                        </div>
                    </div>

                    <div class="app-image">
                        <img src="images/app.PNG" alt="تطبيق محاكي اختبار القدرات المحوسب مجتهد" class="app-screen">
                    </div>
                </div>
            </div>
        </section>

        <!-- الأسئلة الشائعة حول اختبار القدرات -->
        <section class="qudrat-faq">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">أسئلة شائعة عن اختبار القدرات المحوسب</h2>
                    <p class="section-subtitle">إجابات على الأسئلة الشائعة حول اختبار القدرات المحوسب 2025</p>
                </div>

                <div class="faq-grid">
                    <div class="faq-item">
                        <h3>ما هو اختبار القدرات العامة وما أهميته؟</h3>
                        <p>اختبار القدرات العامة هو اختبار يقيس القدرة التحليلية والاستدلالية لدى الطالب، وهو أحد متطلبات القبول في الجامعات السعودية. يشكل نتيجة اختبار القدرات نسبة هامة (حوالي 40%) من النسبة الموزونة للقبول الجامعي.</p>
                    </div>

                    <div class="faq-item">
                        <h3>كم عدد أسئلة اختبار القدرات المحوسب؟</h3>
                        <p>يتكون اختبار القدرات المحوسب من حوالي 120 سؤالاً، مقسمة بين القسم الكمي والقسم اللفظي. تختلف صعوبة الأسئلة حسب مستوى الطالب، حيث يعتمد الاختبار على نظام تكيفي يحدد مستوى صعوبة الأسئلة بناءً على إجابات الطالب السابقة.</p>
                    </div>

                    <div class="faq-item">
                        <h3>ما هي أفضل طريقة للتحضير لاختبار القدرات؟</h3>
                        <p>أفضل طريقة للتحضير هي التدرب على نماذج محاكية للاختبار الرسمي مع حل أكبر عدد ممكن من الأسئلة المشابهة. تطبيق مجتهد يوفر محاكاة دقيقة للاختبار مع شرح تفصيلي لكل سؤال، مما يساعدك على فهم نمط الأسئلة واكتساب المهارات اللازمة.</p>
                    </div>

                    <div class="faq-item">
                        <h3>هل يمكنني استخدام الآلة الحاسبة في اختبار القدرات المحوسب؟</h3>
                        <p>لا، لا يسمح باستخدام الآلة الحاسبة في اختبار القدرات. لكن الاختبار المحوسب يوفر آلة حاسبة أساسية على الشاشة يمكن استخدامها أثناء الاختبار، ومحاكي مجتهد يحاكي نفس هذه الميزة لتعتاد عليها.</p>
                    </div>

                    <div class="faq-item">
                        <h3>هل يمكن تحسين نتيجة اختبار القدرات بإعادة الاختبار؟</h3>
                        <p>نعم، يمكن تحسين النتيجة من خلال التدريب المكثف والتعرف على أنماط الأسئلة والاستراتيجيات الصحيحة للحل. العديد من الطلاب الذين استخدموا محاكي مجتهد استطاعوا تحسين درجاتهم بشكل ملحوظ عند إعادة الاختبار.</p>
                    </div>

                    <div class="faq-item">
                        <h3>ما هي المدة الزمنية المتاحة لاختبار القدرات المحوسب؟</h3>
                        <p>مدة اختبار القدرات المحوسب هي ساعتان ونصف (150 دقيقة) تقريباً، يتم توزيعها على القسم الكمي والقسم اللفظي. محاكي مجتهد يحاكي نفس الزمن المخصص للاختبار الرسمي لتتدرب في ظروف مشابهة للاختبار الحقيقي.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="images/logo1.png" alt="شعار مجتهد - محاكي اختبارات قياس 2025" loading="lazy">
                    <p>تطبيق مجتهد هو الحل الشامل للتفوق في اختبار القدرات المحوسب وجميع اختبارات قياس. يوفر التطبيق محاكيات مطابقة 100% للاختبار الرسمي مع أكثر من 10,000 سؤال محلول وشرح تفصيلي.</p>
                </div>

                <div class="footer-links">
                    <h3 class="footer-title">محاكيات قياس</h3>
                    <a href="qudrat.html" class="footer-link">محاكي القدرات العامة</a>
                    <a href="tahsili.html" class="footer-link">محاكي التحصيلي</a>
                    <a href="step.html" class="footer-link">محاكي STEP</a>
                    <a href="cognitive.html" class="footer-link">محاكي القدرة المعرفية</a>
                    <a href="university.html" class="footer-link">محاكي قدرات الجامعيين</a>
                    <a href="license.html" class="footer-link">محاكي الرخصة المهنية</a>
                </div>

                <div class="footer-links">
                    <h3 class="footer-title">روابط مهمة</h3>
                    <a href="privacy-policy.html" class="footer-link">سياسة الخصوصية</a>
                    <a href="terms.html" class="footer-link">شروط الاستخدام</a>
                    <a href="#" class="footer-link">المساعدة والدعم</a>
                    <a href="#" class="footer-link">اتصل بنا</a>
                    <a href="index.html" class="footer-link">الرئيسية</a>
                </div>

                <div class="footer-contact">
                    <h3 class="footer-title">تواصل معنا</h3>
                    <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span dir="ltr">+966 581105341</span>
                    </div>
                    <div class="contact-info">
                        <i class="fab fa-whatsapp"></i>
                        <span dir="ltr">+966 581105341</span>
                    </div>

                    <div class="social-links">
                        <a href="#" class="social-link" target="_blank" aria-label="تويتر">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link" target="_blank" aria-label="انستغرام">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-link" target="_blank" aria-label="تيك توك">
                            <i class="fab fa-tiktok"></i>
                        </a>
                        <a href="#" class="social-link" target="_blank" aria-label="يوتيوب">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>© 2025 مجتهد - محاكي اختبار القدرات المحوسب | جميع الحقوق محفوظة | التطبيق الأول لتحضير اختبارات قياس في المملكة العربية السعودية</p>
            </div>
        </div>
    </footer>

    <!-- سكريبت محسن لأداء الصفحة -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تحسين أداء الصفحة من خلال استخدام Intersection Observer
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');

                        // تأثير العد للإحصائيات
                        if (entry.target.classList.contains('stat-card')) {
                            animateNumber(entry.target);
                        }

                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });

            // تطبيق المراقبة على العناصر
            document.querySelectorAll('.qudrat-part, .benefit, .app-feature, .faq-item, .stat-card, .testimonial').forEach(item => {
                observer.observe(item);
            });

            // دالة تحريك الأرقام
            function animateNumber(card) {
                const numberElement = card.querySelector('.stat-number');
                if (!numberElement) return;

                const finalNumber = numberElement.textContent;
                const isPercentage = finalNumber.includes('%');
                const isPlus = finalNumber.includes('+');
                const isTime = finalNumber.includes('أسابيع') || finalNumber.includes('أسبوع');
                const isRating = finalNumber.includes('/');

                let targetNumber;
                if (isPercentage) {
                    targetNumber = parseInt(finalNumber);
                } else if (isPlus) {
                    targetNumber = parseInt(finalNumber.replace(/[^0-9]/g, ''));
                } else if (isTime) {
                    targetNumber = parseInt(finalNumber);
                } else if (isRating) {
                    targetNumber = parseFloat(finalNumber);
                } else {
                    targetNumber = parseInt(finalNumber.replace(/[^0-9]/g, ''));
                }

                let currentNumber = 0;
                const increment = targetNumber / 50;
                const timer = setInterval(() => {
                    currentNumber += increment;
                    if (currentNumber >= targetNumber) {
                        currentNumber = targetNumber;
                        clearInterval(timer);
                    }

                    if (isPercentage) {
                        numberElement.textContent = Math.floor(currentNumber) + '%';
                    } else if (isPlus) {
                        numberElement.textContent = Math.floor(currentNumber).toLocaleString() + '+';
                    } else if (isTime) {
                        numberElement.textContent = Math.floor(currentNumber) + ' أسابيع';
                    } else if (isRating) {
                        numberElement.textContent = currentNumber.toFixed(1) + '/5';
                    } else {
                        numberElement.textContent = Math.floor(currentNumber).toLocaleString();
                    }
                }, 40);
            }

            // تأثير التمرير السلس للروابط الداخلية
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // تأثير التمرير للصور
            const images = document.querySelectorAll('.qudrat-preview, .app-screen');
            images.forEach(img => {
                img.addEventListener('mouseenter', function() {
                    this.style.transform = 'perspective(1000px) rotateY(0deg) rotateX(0deg) scale(1.05)';
                });

                img.addEventListener('mouseleave', function() {
                    if (this.classList.contains('qudrat-preview')) {
                        this.style.transform = 'perspective(1000px) rotateY(-10deg) rotateX(5deg)';
                    } else {
                        this.style.transform = 'perspective(1000px) rotateY(10deg) rotateX(-5deg)';
                    }
                });
            });

            // تتبع النقرات على أزرار التحميل
            document.querySelectorAll('.download-btn, .primary-btn, .secondary-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // إضافة تأثير النقر
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);

                    // تتبع النقرات (يمكن إضافة Google Analytics هنا)
                    console.log('Button clicked:', this.textContent.trim());
                });
            });
        });
    </script>
</body>
</html>